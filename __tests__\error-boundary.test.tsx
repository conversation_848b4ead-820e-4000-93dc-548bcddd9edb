import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { jest } from '@jest/globals';
import { ErrorBoundary, ErrorFallback, withErrorBoundary } from '@/components/error-boundary';

// A component that throws an error for testing purposes
const ErrorThrowingComponent = ({ shouldThrow = true }: { shouldThrow?: boolean; }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error thrown</div>;
};

// A component wrapped with the HOC for testing
const WrappedComponent = withErrorBoundary(
  ErrorThrowingComponent,
  {
    onError: jest.fn(),
    className: 'test-class'
  }
);

describe('ErrorBoundary', () => {
  // Suppress console errors during tests
  const originalConsoleError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });
  
  afterAll(() => {
    console.error = originalConsoleError;
  });

  it('renders children when no error occurs', () => {
    render(
      <ErrorBoundary>
        <div data-testid="child">Child content</div>
      </ErrorBoundary>
    );
    
    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('renders fallback UI when an error occurs', () => {
    // We need to spy on console.error to prevent test failures due to React's error logging
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <ErrorBoundary>
        <ErrorThrowingComponent />
      </ErrorBoundary>
    );
    
    // Check that the fallback UI is rendered
    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
    expect(screen.getByText(/Test error/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Try again/i })).toBeInTheDocument();
  });

  it('calls onError when provided and an error occurs', () => {
    const onErrorMock = jest.fn();
    
    // We need to spy on console.error to prevent test failures due to React's error logging
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <ErrorBoundary onError={onErrorMock}>
        <ErrorThrowingComponent />
      </ErrorBoundary>
    );
    
    expect(onErrorMock).toHaveBeenCalled();
  });

  it('resets the error state when the "Try again" button is clicked', () => {
    // Mock the window.location.reload function
    const reloadMock = jest.fn();
    Object.defineProperty(window, 'location', {
      value: { reload: reloadMock },
      writable: true
    });
    
    // We need to spy on console.error to prevent test failures due to React's error logging
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <ErrorBoundary>
        <ErrorThrowingComponent />
      </ErrorBoundary>
    );
    
    // Click the "Refresh Page" button
    fireEvent.click(screen.getByRole('button', { name: /Refresh Page/i }));
    
    // Check that window.location.reload was called
    expect(reloadMock).toHaveBeenCalled();
  });
});

describe('ErrorFallback', () => {
  it('renders error message and retry button', () => {
    const error = new Error('Test error message');
    const resetErrorBoundary = jest.fn();
    
    render(
      <ErrorFallback 
        error={error} 
        resetErrorBoundary={resetErrorBoundary} 
      />
    );
    
    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
    expect(screen.getByText(/Test error message/i)).toBeInTheDocument();
    
    // Click the retry button
    fireEvent.click(screen.getByRole('button', { name: /Try again/i }));
    expect(resetErrorBoundary).toHaveBeenCalled();
  });
});

describe('withErrorBoundary HOC', () => {
  it('wraps component with error boundary', () => {
    // We need to spy on console.error to prevent test failures due to React's error logging
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(<WrappedComponent />);
    
    // Check that the fallback UI is rendered
    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
  });
  
  it('passes props to wrapped component', () => {
    render(<WrappedComponent shouldThrow={false} />);
    
    // Check that the component rendered without error
    expect(screen.getByText(/No error thrown/i)).toBeInTheDocument();
  });
  
  it('applies className to the error boundary container', () => {
    // We need to spy on console.error to prevent test failures due to React's error logging
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    const { container } = render(<WrappedComponent />);
    
    // Check that the className was applied
    expect(container.firstChild).toHaveClass('test-class');
  });
});
