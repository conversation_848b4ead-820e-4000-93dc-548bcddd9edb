import { useCallback } from 'react';

interface ErrorLoggerOptions {
  applicationName?: string;
  environment?: string;
  additionalContext?: Record<string, unknown>;
  shouldCaptureBreadcrumbs?: boolean;
}

/**
 * A hook for logging errors to a monitoring service
 * This can be connected to services like Sentry, LogRocket, or a custom backend
 */
export function useErrorLogger(options: ErrorLoggerOptions = {}) {
  const {
    applicationName = 'BidManager',
    environment = process.env.NODE_ENV || 'development',
    additionalContext = {},
    shouldCaptureBreadcrumbs = true,
  } = options;

  /**
   * Log an error to the monitoring service
   */
  const logError = useCallback(
    (error: Error, errorInfo?: React.ErrorInfo) => {
      // In development, always log to console
      if (environment === 'development') {
        console.error('Error captured by error logger:', error);
        if (errorInfo) {
          console.error('Component stack:', errorInfo.componentStack);
        }
      }

      // Here you would integrate with your error monitoring service
      // Example with Sentry (if it were installed):
      // 
      // if (typeof window !== 'undefined' && window.Sentry) {
      //   window.Sentry.withScope((scope) => {
      //     if (errorInfo) {
      //       scope.setExtras({ componentStack: errorInfo.componentStack });
      //     }
      //     
      //     Object.entries(additionalContext).forEach(([key, value]) => {
      //       scope.setExtra(key, value);
      //     });
      //     
      //     scope.setTag('application', applicationName);
      //     window.Sentry.captureException(error);
      //   });
      // }

      // You can also send to your own API endpoint
      try {
        // Example of sending to a custom API endpoint:
        // const errorData = {
        //   message: error.message,
        //   name: error.name,
        //   stack: error.stack,
        //   componentStack: errorInfo?.componentStack,
        //   timestamp: new Date().toISOString(),
        //   applicationName,
        //   environment,
        //   ...additionalContext,
        // };
        // 
        // fetch('/api/log-error', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(errorData),
        // }).catch(e => console.error('Failed to send error to API:', e));
      } catch (e) {
        console.error('Error while trying to log error:', e);
      }
    },
    [applicationName, environment, additionalContext, shouldCaptureBreadcrumbs]
  );

  /**
   * Wrap a function with error logging
   */
  const withErrorLogging = useCallback(
    <T extends (...args: any[]) => any>(fn: T): T => {
      return ((...args: Parameters<T>): ReturnType<T> => {
        try {
          return fn(...args);
        } catch (error) {
          logError(error as Error);
          throw error; // Re-throw the error after logging
        }
      }) as T;
    },
    [logError]
  );

  return {
    logError,
    withErrorLogging,
  };
}
