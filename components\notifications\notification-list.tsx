'use client';

import { useState, useEffect, useCallback } from 'react';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bell, CheckCircle2, MessageCircle, FileCheck, RefreshCw, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { withErrorBoundary } from '@/components/error-boundary';

interface Notification {
  id: string;
  vendorId: string;
  type: 'submission' | 'query' | 'screening' | 'automated_check' | 'status_update';
  title: string;
  message: string;
  status: 'unread' | 'read';
  createdAt: string;
  metadata?: {
    bidId?: string;
    queryId?: string;
    documentId?: string;
    checkResults?: {
      passed: number;
      failed: number;
      warnings: number;
    };
  };
}

interface NotificationListProps {
  vendorId: string;
  onNotificationClick?: (notification: Notification) => void;
}

function NotificationListComponent({ vendorId, onNotificationClick }: NotificationListProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchNotifications = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/notifications?vendorId=${vendorId}`);
      if (!response.ok) throw new Error('Failed to fetch notifications');
      const data = await response.json();
      setNotifications(data);
      setError(null);
    } catch (err) {
      setError('Error loading notifications');
      console.error('Error fetching notifications:', err);
    } finally {
      setIsLoading(false);
    }
  }, [vendorId]);

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: notificationId,
          status: 'read',
        }),
      });

      if (!response.ok) throw new Error('Failed to update notification');

      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId ? { ...n, status: 'read' } : n
        )
      );
    } catch (err) {
      console.error('Error updating notification:', err);
    }
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'submission':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'query':
        return <MessageCircle className="h-4 w-4 text-blue-500" />;
      case 'screening':
        return <FileCheck className="h-4 w-4 text-purple-500" />;
      case 'automated_check':
        return <RefreshCw className="h-4 w-4 text-orange-500" />;
      case 'status_update':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <RefreshCw className="h-5 w-5 animate-spin" />
        <span className="ml-2">Loading notifications...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-4 text-red-500">
        <AlertTriangle className="h-5 w-5 mr-2" />
        {error}
      </div>
    );
  }

  return (
    <ScrollArea className="h-[400px]">
      <div className="space-y-4 p-4">
        {notifications.length === 0 ? (
          <div className="text-center text-gray-500">
            No notifications
          </div>
        ) : (
          notifications.map((notification) => (
            <div
              key={notification.id}
              className={cn(
                "rounded-lg border p-4 transition-colors hover:bg-accent cursor-pointer",
                notification.status === 'unread' && "bg-accent/50"
              )}
              onClick={() => {
                markAsRead(notification.id);
                onNotificationClick?.(notification);
              }}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  {getNotificationIcon(notification.type)}
                  <div>
                    <h4 className="text-sm font-medium">
                      {notification.title}
                      {notification.status === 'unread' && (
                        <Badge variant="secondary" className="ml-2">
                          New
                        </Badge>
                      )}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {formatDate(notification.createdAt)}
                    </p>
                  </div>
                </div>
                {notification.metadata?.checkResults && (
                  <div className="flex items-center space-x-2">
                    <Badge variant="default" className="bg-green-500">
                      {notification.metadata.checkResults.passed} passed
                    </Badge>
                    {notification.metadata.checkResults.warnings > 0 && (
                      <Badge variant="secondary" className="bg-yellow-500">
                        {notification.metadata.checkResults.warnings} warnings
                      </Badge>
                    )}
                    {notification.metadata.checkResults.failed > 0 && (
                      <Badge variant="destructive">
                        {notification.metadata.checkResults.failed} failed
                      </Badge>
                    )}
                  </div>
                )}
              </div>
              <p className="mt-1 text-sm">{notification.message}</p>
            </div>
          ))
        )}
      </div>
    </ScrollArea>
  );
}

// Export the component wrapped with an error boundary
export const NotificationList = withErrorBoundary(NotificationListComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in NotificationList component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full'
});
