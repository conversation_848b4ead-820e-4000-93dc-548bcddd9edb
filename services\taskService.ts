import axios from 'axios';
import { getSAMLArtifact } from '@/lib/samlUtils';
import { TaskListRequest, TaskListResponse, TaskItem, TaskFilterParams } from '@/types/task';
import { AppworksFilterParameter } from '@/types';

/**
 * Converts application task filter parameters to Appworks API filter format
 * @param filters - The filter parameters from the application
 * @returns An object with Appworks-formatted filter parameters
 */
const buildTaskFilterParameters = (filters?: TaskFilterParams): Record<string, AppworksFilterParameter> => {
  if (!filters) return {};

  const parameters: Record<string, AppworksFilterParameter> = {};

  // Subject filter (using Like operator for partial matches)
  if (filters.subject) {
    parameters['Task.Subject'] = {
      name: 'Task.Subject',
      comparison: {
        value: `*${filters.subject}*`, // Wildcard search
        operator: 'Like'
      }
    };
  }

  // Task state filter (exact match)
  if (filters.state && filters.state !== 'all') {
    parameters['Task.State'] = {
      name: 'Task.State',
      comparison: {
        value: filters.state,
        operator: 'eq'
      }
    };
  }

  // Bid title filter (using Like operator for partial matches)
  if (filters.bidTitle) {
    parameters['ParentEntity$Properties.Bid_Title'] = {
      name: 'ParentEntity$Properties.Bid_Title',
      comparison: {
        value: `*${filters.bidTitle}*`, // Wildcard search
        operator: 'Like'
      }
    };
  }

  // Bid status filter (exact match)
  if (filters.bidStatus && filters.bidStatus !== 'all') {
    parameters['ParentEntity$Properties.Bid_Status'] = {
      name: 'ParentEntity$Properties.Bid_Status',
      comparison: {
        value: filters.bidStatus,
        operator: 'eq'
      }
    };
  }

  // Task owner filter (exact match)
  if (filters.taskOwner) {
    parameters['Task.TaskOwner'] = {
      name: 'Task.TaskOwner',
      comparison: {
        value: filters.taskOwner,
        operator: 'eq'
      }
    };
  }

  // Budget range filter (using Between operator)
  if (filters.budgetMin !== undefined && filters.budgetMax !== undefined) {
    parameters['ParentEntity$Properties.Bid_Budget'] = {
      name: 'ParentEntity$Properties.Bid_Budget',
      comparison: {
        from: filters.budgetMin.toString(),
        to: filters.budgetMax.toString(),
        operator: 'Between'
      }
    };
  } else if (filters.budgetMin !== undefined) {
    parameters['ParentEntity$Properties.Bid_Budget'] = {
      name: 'ParentEntity$Properties.Bid_Budget',
      comparison: {
        value: filters.budgetMin.toString(),
        operator: 'ge' // Greater than or equal
      }
    };
  } else if (filters.budgetMax !== undefined) {
    parameters['ParentEntity$Properties.Bid_Budget'] = {
      name: 'ParentEntity$Properties.Bid_Budget',
      comparison: {
        value: filters.budgetMax.toString(),
        operator: 'le' // Less than or equal
      }
    };
  }

  // Created date range filter (using Between operator)
  if (filters.createdDateFrom && filters.createdDateTo) {
    parameters['ParentEntity$Tracking.CreatedDate'] = {
      name: 'ParentEntity$Tracking.CreatedDate',
      comparison: {
        from: filters.createdDateFrom,
        to: filters.createdDateTo,
        operator: 'Between'
      }
    };
  } else if (filters.createdDateFrom) {
    parameters['ParentEntity$Tracking.CreatedDate'] = {
      name: 'ParentEntity$Tracking.CreatedDate',
      comparison: {
        value: filters.createdDateFrom,
        operator: 'ge' // Greater than or equal
      }
    };
  } else if (filters.createdDateTo) {
    parameters['ParentEntity$Tracking.CreatedDate'] = {
      name: 'ParentEntity$Tracking.CreatedDate',
      comparison: {
        value: filters.createdDateTo,
        operator: 'le' // Less than or equal
      }
    };
  }

  return parameters;
};

/**
 * Enhanced error handling for task operations
 * @param error - The error object
 * @param operation - The operation that failed
 * @returns A user-friendly error message
 */
const handleTaskError = (error: any, operation: string): string => {
  if (axios.isAxiosError(error)) {
    // Handle specific axios errors
    if (error.code === 'ECONNABORTED') {
      return `${operation} timed out. Please try again.`;
    } else if (error.code === 'ERR_FR_TOO_MANY_REDIRECTS') {
      return "Authentication error. Please log in again.";
    } else if (error.response) {
      // Server responded with a non-2xx status
      const status = error.response.status;
      if (status === 401) {
        return "Authentication required. Please log in again.";
      } else if (status === 403) {
        return "You don't have permission to access tasks.";
      } else if (status === 404) {
        return "Task service not found. Please contact support.";
      } else if (status >= 500) {
        return "Server error. Please try again later.";
      }
      return `${operation} failed: ${error.response.data?.error || error.message}`;
    } else if (error.request) {
      // Request was made but no response received
      return "No response from server. Please check your connection.";
    }
  }

  return `${operation} failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
};

/**
 * Fetches tasks from the Appworks API through the proxy endpoint
 * @param filters - Optional filter parameters
 * @returns Promise with the task list response
 */
export async function fetchTasks(filters?: TaskFilterParams, requestOptions?: Partial<TaskListRequest>): Promise<TaskListResponse> {
  try {
    console.log('Fetching tasks through proxy API...');

    // Get the SAML artifact for authentication
    const samlArtifact = getSAMLArtifact();

    if (!samlArtifact) {
      console.warn('No SAML artifact found for task fetch');
      // Return empty response structure to match the expected type
      return {
        page: { skip: 0, top: 0, count: 0 },
        _links: { self: { href: '' }, first: { href: '' } },
        _embedded: { BidDefaultTaskList: [] }
      };
    }

    // Build filter parameters if provided
    const filterParameters = buildTaskFilterParameters(filters);

    // Default task state filter (active tasks)
    const defaultParameters = {
      'Task.State': {
        name: 'Task.State',
        comparison: {
          value: '2', // Active task state
          operator: 'eq' as const
        }
      }
    };

    // Merge default parameters with filter parameters
    const parameters = { ...defaultParameters, ...filterParameters };

    // Build the request payload to match the working API test
    const requestPayload = {
      distinct: requestOptions?.distinct || 0,
      skip: requestOptions?.skip || 0,
      top: requestOptions?.top || 0, // Use 0 to get all results (matching successful test)
      parameters,
      orderBy: requestOptions?.orderBy || [
        {
          name: 'ParentEntity$Tracking.CreatedDate',
          sortType: 'DESC' as const
        }
      ],
      select: requestOptions?.select || [] // Empty select to get all fields
    };

    console.log('Fetching tasks through proxy API with filters:', filters ? JSON.stringify(filters) : 'none');

    // Use the child entity proxy API endpoint for tasks
    const response = await axios.post<TaskListResponse>(
      '/api/proxy/appworks/child-entities?parentEntityType=Bid&childEntityType=LifecycleTask&listName=BidDefaultTaskList',
      requestPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${samlArtifact.value}`
        }
      }
    );

    console.log('Successfully fetched tasks:', {
      taskCount: response.data?._embedded?.BidDefaultTaskList?.length || 0,
    });

    return response.data;
  } catch (error) {
    const errorMessage = handleTaskError(error, 'Fetching tasks');
    console.error('Error fetching tasks from proxy API:', {
      message: errorMessage,
      originalError: error,
      status: axios.isAxiosError(error) ? error.response?.status : undefined,
      data: axios.isAxiosError(error) ? error.response?.data : undefined
    });

    // Return empty response on error to prevent UI crashes
    return {
      page: { skip: 0, top: 0, count: 0 },
      _links: { self: { href: '' }, first: { href: '' } },
      _embedded: { BidDefaultTaskList: [] }
    };
  }
}

/**
 * Gets tasks assigned to the current user
 * @param filters - Optional filter parameters
 * @param requestOptions - Optional request configuration
 * @returns Promise with an array of tasks assigned to the current user
 */
export async function getMyTasks(filters?: TaskFilterParams, requestOptions?: Partial<TaskListRequest>): Promise<TaskItem[]> {
  try {
    console.log('Fetching user tasks...');

    // Fetch tasks with optional filters
    const response = await fetchTasks(filters, requestOptions);

    const tasks = response._embedded?.BidDefaultTaskList || [];
    console.log(`Found ${tasks.length} tasks assigned to the user`);

    return tasks;
  } catch (error) {
    console.error('Error fetching user tasks:', error);
    return [];
  }
}

/**
 * Updates a task's status
 * @param taskId - The ID of the task to update
 * @param status - The new status
 * @returns Promise that resolves when the update is complete
 */
export async function updateTaskStatus(taskId: string, status: string): Promise<boolean> {
  try {
    console.log(`Updating task ${taskId} status to ${status}`);

    // Get the SAML artifact for authentication
    const samlArtifact = getSAMLArtifact();

    if (!samlArtifact) {
      throw new Error('Authentication token not found. Please log in again.');
    }

    // Prepare the task update payload
    const updatePayload = {
      Task: {
        State: status
      }
    };

    // Use the proxy API endpoint to update the task
    const response = await axios.put(
      `/api/proxy/appworks/entities/LifecycleTask/items/${taskId}`,
      updatePayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${samlArtifact.value}`
        }
      }
    );

    // API returns 204 No Content on success
    const success = response.status === 204;
    console.log(`Task ${taskId} status update ${success ? 'successful' : 'failed'}`);

    return success;
  } catch (error) {
    const errorMessage = handleTaskError(error, 'Updating task status');
    console.error('Error updating task status:', {
      taskId,
      status,
      message: errorMessage,
      originalError: error
    });

    // Re-throw with user-friendly message
    throw new Error(errorMessage);
  }
}
