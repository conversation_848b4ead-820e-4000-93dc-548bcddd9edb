"use client";

import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { Bid } from "@/types";
import { updateBid } from "@/lib/api";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { withErrorBoundary } from "@/components/error-boundary";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Define the form schema with Zod
const formSchema = z.object({
  title: z.string().min(2, {
    message: "Title must be at least 2 characters.",
  }),
  description: z.string().min(5, {
    message: "Description must be at least 5 characters.",
  }),
  budget: z.coerce.number().min(0, {
    message: "Budget must be a positive number.",
  }),
  status: z.enum(["DRAFT", "PENDING", "APPROVED", "REJECTED", "CLOSED"]),
  dueDate: z.date().optional(),
  opportunitySource: z.string().optional(),
  businessNeed: z.string().optional(),
  strategicAlignment: z.string().optional(),
  potentialBenefits: z.string().optional(),
  preliminaryScope: z.string().optional(),
  stakeholders: z.string().optional(),
  riskAssessment: z.string().optional(),
  bidStrategy: z.string().optional(),
  competitiveAnalysis: z.string().optional(),
  winStrategy: z.string().optional(),
  technicalApproach: z.string().optional(),
  pricingStrategy: z.string().optional(),
  qualityStandards: z.string().optional(),
  complianceRequirements: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface EditBidDialogProps {
  bid: Bid;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onBidUpdated: (updatedBid: Bid) => void;
}

function EditBidDialogComponent({
  bid,
  open,
  onOpenChange,
  onBidUpdated,
}: EditBidDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Initialize form with react-hook-form and zod validation
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      budget: 0,
      status: "DRAFT" as const,
      opportunitySource: "",
      businessNeed: "",
      strategicAlignment: "",
      potentialBenefits: "",
      preliminaryScope: "",
      stakeholders: "",
      riskAssessment: "",
      bidStrategy: "",
      competitiveAnalysis: "",
      winStrategy: "",
      technicalApproach: "",
      pricingStrategy: "",
      qualityStandards: "",
      complianceRequirements: "",
    },
  });

  // Update form values when bid changes
  useEffect(() => {
    if (bid) {
      // Parse the due date string into a Date object if it exists
      let dueDateValue: Date | undefined = undefined;
      
      if (bid.dueDate) {
        try {
          // Handle different date formats that might come from the API
          dueDateValue = new Date(bid.dueDate);
          
          // Check if the date is valid
          if (isNaN(dueDateValue.getTime())) {
            console.warn(`Invalid date format for dueDate: ${bid.dueDate}`);
            dueDateValue = undefined;
          } else {
            console.log(`Parsed dueDate: ${dueDateValue.toISOString()}`);
          }
        } catch (error) {
          console.error(`Error parsing dueDate: ${bid.dueDate}`, error);
        }
      }
      
      form.reset({
        title: bid.title || "",
        description: bid.description || "",
        budget: bid.budget || 0,
        status: bid.status || "DRAFT",
        dueDate: dueDateValue,
        opportunitySource: bid.opportunitySource || "",
        businessNeed: bid.businessNeed || "",
        strategicAlignment: bid.strategicAlignment || "",
        potentialBenefits: bid.potentialBenefits || "",
        preliminaryScope: bid.preliminaryScope || "",
        stakeholders: bid.stakeholders || "",
        riskAssessment: bid.riskAssessment || "",
        bidStrategy: bid.bidStrategy || "",
        competitiveAnalysis: bid.competitiveAnalysis || "",
        winStrategy: bid.winStrategy || "",
        technicalApproach: bid.technicalApproach || "",
        pricingStrategy: bid.pricingStrategy || "",
        qualityStandards: bid.qualityStandards || "",
        complianceRequirements: bid.complianceRequirements || "",
      });
    }
  }, [bid, form]);

  const handleSubmit = async (values: FormValues) => {
    if (!bid) return;

    setIsSubmitting(true);

    try {
      // Format date for API
      const formattedDueDate = values.dueDate 
        ? format(values.dueDate, "yyyy-MM-dd") 
        : undefined;
      
      // Update the bid via API
      const success = await updateBid(bid.bidId, {
        ...values,
        dueDate: formattedDueDate,
      });

      if (success) {
        toast({
          title: "Bid updated",
          description: "The bid has been successfully updated",
        });

        // Create updated bid object to refresh UI
        const updatedBid: Bid = {
          ...bid,
          ...values,
          dueDate: formattedDueDate || bid.dueDate,
          updatedAt: new Date().toISOString(),
        };

        onBidUpdated(updatedBid);
        onOpenChange(false);
      } else {
        throw new Error("Failed to update bid");
      }
    } catch (error) {
      console.error("Error updating bid:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update bid",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Bid</DialogTitle>
          <DialogDescription>
            Update the bid details. Click save when you&apos;re done.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information Section */}
              <div className="space-y-4 md:col-span-2">
                <h3 className="text-lg font-medium">Basic Information</h3>
                
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Bid title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Bid description" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="budget"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="0" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="DRAFT">Draft</SelectItem>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="APPROVED">Approved</SelectItem>
                            <SelectItem value="REJECTED">Rejected</SelectItem>
                            <SelectItem value="CLOSED">Closed</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="dueDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Due Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={`w-full pl-3 text-left font-normal ${
                                !field.value && "text-muted-foreground"
                              }`}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={(date) => {
                              console.log("Calendar date selected:", date);
                              field.onChange(date);
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Strategic Information Section */}
              <div className="space-y-4 col-span-2">
                <h3 className="text-lg font-medium">Strategic Information</h3>
                
                <FormField
                  control={form.control}
                  name="opportunitySource"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Opportunity Source</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select opportunity source" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="rfp">RFP/RFQ</SelectItem>
                          <SelectItem value="direct">Direct Customer Request</SelectItem>
                          <SelectItem value="market">Market Research</SelectItem>
                          <SelectItem value="internal">Internal Initiative</SelectItem>
                          <SelectItem value="partner">Partner Referral</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="businessNeed"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Business Need</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What business need does this address?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="strategicAlignment"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Strategic Alignment</FormLabel>
                      <FormControl>
                        <Textarea placeholder="How does this align with our strategy?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="potentialBenefits"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Potential Benefits</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What are the potential benefits?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Scope & Planning Section */}
              <div className="space-y-4 col-span-2">
                <h3 className="text-lg font-medium">Scope & Planning</h3>
                
                <FormField
                  control={form.control}
                  name="preliminaryScope"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preliminary Scope</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What is the scope of this bid?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="stakeholders"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stakeholders</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Who are the key stakeholders?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="riskAssessment"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Risk Assessment</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What are the potential risks?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Strategy Section */}
              <div className="space-y-4 col-span-2">
                <h3 className="text-lg font-medium">Strategy</h3>
                
                <FormField
                  control={form.control}
                  name="bidStrategy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bid Strategy</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What is our strategy for this bid?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="competitiveAnalysis"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Competitive Analysis</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Who are our competitors and how do we compare?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="winStrategy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Win Strategy</FormLabel>
                      <FormControl>
                        <Textarea placeholder="How will we win this bid?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Technical & Compliance Section */}
              <div className="space-y-4 col-span-2">
                <h3 className="text-lg font-medium">Technical & Compliance</h3>
                
                <FormField
                  control={form.control}
                  name="technicalApproach"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Technical Approach</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What is our technical approach?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pricingStrategy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pricing Strategy</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What is our pricing strategy?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="qualityStandards"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quality Standards</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What quality standards will we adhere to?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="complianceRequirements"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Compliance Requirements</FormLabel>
                      <FormControl>
                        <Textarea placeholder="What compliance requirements must we meet?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

// Export the component wrapped with an error boundary
export const EditBidDialog = withErrorBoundary(EditBidDialogComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in EditBidDialog component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full'
});
