import { Bid } from "@/types";
import DocumentEditorClient from "./document-editor-client";
import { withErrorBoundary } from "@/components/error-boundary";

// Server component that renders the client component
function DocumentEditorPage({
  params,
}: {
  params: { bidId: string; documentId: string };
}) {
  return <DocumentEditorClient bidId={params.bidId} documentId={params.documentId} />;
}

// Export the component wrapped with an error boundary
export default withErrorBoundary(DocumentEditorPage, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Document Editor page:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});
