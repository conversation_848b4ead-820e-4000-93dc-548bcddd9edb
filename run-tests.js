// Simple script to run tests with detailed output
const { execSync } = require('child_process');

try {
  console.log('Running SQLiteDB tests with detailed output...');
  const output = execSync('npx jest __tests__/sqlitedb.test.ts --no-cache --verbose', { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  console.log('Test Results:');
  console.log(output);
  console.log('Tests completed successfully!');
} catch (error) {
  console.log('Test execution failed:');
  if (error.stdout) {
    console.log('STDOUT:');
    console.log(error.stdout);
  }
  if (error.stderr) {
    console.log('STDERR:');
    console.log(error.stderr);
  }
}
