# Knowledge Graph Visualization

```mermaid
graph TD
    %% API Documentation
    subgraph "API Documentation"
        AppworksAPIs["Appworks REST APIs"]
    end
    
    %% Bid Management APIs
    subgraph "Bid Management"
        ReadBid["READ Bid API"]
        CreateBid["CREATE Bid API"]
        RetrieveBids["RETRIEVE Bids API"]
        UpdateBid["UPDATE Bid API"]
    end
    
    %% Authentication
    subgraph "Authentication"
        SAMLFlow["SAML Authentication"]
        LoginAPI["Login API"]
        SessionAPI["Session API"]
        LogoutAPI["Logout API"]
        OTDS["OTDS"]
        
        SAMLFlow --> LoginAPI
        SAMLFlow --> SessionAPI
        SAMLFlow --> LogoutAPI
        SAMLFlow --> OTDS
    end
    
    %% Task Management
    subgraph "Task Management"
        TasksAPI["Appworks Tasks API"]
        TasksProxy["Tasks Proxy"]
        TaskService["Task Service"]
        TaskServiceImplementation["Task Service Implementation"]
        
        TaskServiceImplementation --> TaskService
        TaskService --> TasksProxy
        TasksProxy --> TasksAPI
    end
    
    %% Relationships
    ReadBid -->|documented_in| AppworksAPIs
    CreateBid -->|documented_in| AppworksAPIs
    RetrieveBids -->|documented_in| AppworksAPIs
    UpdateBid -->|documented_in| AppworksAPIs
    
    classDef api fill:#f9f,stroke:#333,stroke-width:2px;
    classDef auth fill:#bbf,stroke:#333,stroke-width:2px;
    classDef task fill:#bfb,stroke:#333,stroke-width:2px;
    classDef doc fill:#fbb,stroke:#333,stroke-width:2px;
    
    class ReadBid,CreateBid,RetrieveBids,UpdateBid api;
    class SAMLFlow,LoginAPI,SessionAPI,LogoutAPI,OTDS auth;
    class TasksAPI,TasksProxy,TaskService task;
    class AppworksAPIs doc;
```

## Legend
- **Pink Nodes**: API Documentation
- **Blue Nodes**: Authentication Components
- **Green Nodes**: Task Management Components
- **Purple Nodes**: Individual API Endpoints

## How to Use
1. This Mermaid diagram can be viewed in any Markdown viewer that supports Mermaid.js
2. In VS Code, you can use the "Markdown Preview Enhanced" extension
3. For interactive viewing, you can use the [Mermaid Live Editor](https://mermaid.live/)

## Key Relationships
- **Detailed Observations**: For detailed observations and API specifications, refer to the knowledge graph directly.

- **Documentation**: Shows how API endpoints are documented
- **Authentication Flow**: Illustrates the SAML authentication process
- **Task Management**: Shows the relationship between the Task Service, Proxy, and underlying API

## Notes
- The diagram is interactive in supporting viewers
- Hover over nodes to see additional details
- Click on nodes to expand/collapse sections in some viewers
- This diagram provides a high-level overview. For detailed observations and specifications, query the knowledge graph directly.
