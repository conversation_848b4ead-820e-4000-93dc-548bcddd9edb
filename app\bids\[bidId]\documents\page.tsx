import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Bid Documents",
  description: "Manage documents for this bid",
};

import { DocumentsPage } from "@/components/documents";
import { withErrorBoundary } from "@/components/error-boundary";

function Page({ params }: { params: { bidId: string } }) {
  return <DocumentsPage bidId={params.bidId} />;
}

// Export the component wrapped with an error boundary
export default withErrorBoundary(Page, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Documents page:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});
