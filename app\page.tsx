"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ArrowRight,
  BarChart2,
  Clock,
  FileText,
  PieChart,
  Plus,
  TrendingUp,
  Users,
  Calendar,
  AlertCircle,
  Loader2,
  RefreshCw
} from "lucide-react";
import Link from "next/link";
import { Progress } from "@/components/ui/progress";
import { useEffect, useState } from "react";
import { fetchDashboardStats, DashboardStats } from "@/lib/dashboardApi";
import { fetchBids } from "@/lib/api";
import { Bid } from "@/types";
import { format } from "date-fns";
import { StatusDistribution } from "@/components/dashboard/status-distribution";
import { BidTrend } from "@/components/dashboard/bid-trend";
import { ActivityFeed } from "@/components/dashboard/activity-feed";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { withErrorBoundary } from "@/components/error-boundary";

function Home() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<{
    totalBids: number;
    activeBids: number;
    pendingBids: number;
    totalBudget: number;
    bidProgress: number;
    bidsByStatus: Record<string, number>;
    bidsByMonth: Array<{ month: string; count: number }>;
    recentActivity: Array<{
      id: string;
      type: 'bid_created' | 'bid_updated' | 'status_changed';
      title: string;
      timestamp: string;
      details?: string;
    }>;
  }>({
    totalBids: 0,
    activeBids: 0,
    pendingBids: 0,
    totalBudget: 0,
    bidProgress: 0,
    bidsByStatus: {},
    bidsByMonth: [],
    recentActivity: []
  });
  const [upcomingDeadlines, setUpcomingDeadlines] = useState<Bid[]>([]);
  const [activeTab, setActiveTab] = useState<string>("overview");

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);

        // Fetch dashboard statistics
        const dashboardStats = await fetchDashboardStats();

        // Calculate completion rate
        const bidProgress = dashboardStats.totalBids > 0
          ? (dashboardStats.activeBids / dashboardStats.totalBids) * 100
          : 0;

        // Fetch bids to get upcoming deadlines
        const bids = await fetchBids();
        const upcoming = bids
          .filter(bid => bid.dueDate && new Date(bid.dueDate) > new Date())
          .sort((a, b) => {
            if (!a.dueDate || !b.dueDate) return 0;
            return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
          })
          .slice(0, 3);

        // Update state with fetched data
        setStats({
          totalBids: dashboardStats.totalBids,
          activeBids: dashboardStats.activeBids,
          pendingBids: dashboardStats.pendingBids,
          totalBudget: dashboardStats.totalBudget,
          bidProgress,
          bidsByStatus: dashboardStats.bidsByStatus || {},
          bidsByMonth: dashboardStats.bidsByMonth || [],
          recentActivity: dashboardStats.recentActivity || []
        });

        setUpcomingDeadlines(upcoming);
      } catch (error) {
        console.error("Failed to load dashboard data:", error);
        setError("Failed to load dashboard data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  const statsConfig = [
    {
      title: "Total Bids",
      value: stats.totalBids,
      description: "All bids in the system",
      icon: FileText,
      color: "text-blue-500",
    },
    {
      title: "Active Bids",
      value: stats.activeBids,
      description: `${stats.bidProgress.toFixed(0)}% completion rate`,
      icon: TrendingUp,
      color: "text-green-500",
      progress: stats.bidProgress
    },
    {
      title: "Pending Review",
      value: stats.pendingBids,
      description: "Bids awaiting approval",
      icon: Clock,
      color: "text-yellow-500",
    },
    {
      title: "Total Budget",
      value: `$${stats.totalBudget.toLocaleString()}`,
      description: "Combined bid value",
      icon: PieChart,
      color: "text-purple-500",
    },
  ];

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 h-[60vh] flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto py-8 h-[60vh] flex items-center justify-center">
        <div className="flex flex-col items-center gap-4 max-w-md text-center">
          <AlertCircle className="h-10 w-10 text-destructive" />
          <h2 className="text-xl font-semibold">Error Loading Dashboard</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-4xl font-bold">Bid Management Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Track, manage, and analyze all your bids in one place
          </p>
        </div>
        <div className="flex gap-4">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
            className="mr-2"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Link href="/bids">
            <Button variant="outline">
              <FileText className="mr-2 h-5 w-5" />
              View All Bids
            </Button>
          </Link>
          <Link href="/bids/new">
            <Button>
              <Plus className="mr-2 h-5 w-5" />
              Create New Bid
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsConfig.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-5 w-5 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {stat.description}
              </p>
              {stat.progress !== undefined && (
                <Progress
                  value={stat.progress}
                  className="h-2 mt-3"
                />
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Status Distribution Chart */}
            <div className="col-span-1 md:col-span-2">
              <StatusDistribution data={stats.bidsByStatus} />
            </div>

            {/* Upcoming deadlines card */}
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Deadlines</CardTitle>
                <CardDescription>
                  Bids with approaching due dates
                </CardDescription>
              </CardHeader>
              <CardContent>
                {upcomingDeadlines.length > 0 ? (
                  <ul className="space-y-3">
                    {upcomingDeadlines.map((bid) => (
                      <li key={bid.bidId} className="flex items-start">
                        <Calendar className="h-5 w-5 mr-3 text-muted-foreground shrink-0" />
                        <div>
                          <Link
                            href={`/bids/${bid.bidId}`}
                            className="font-medium hover:underline"
                          >
                            {bid.title}
                          </Link>
                          <div className="text-xs text-muted-foreground">
                            {bid.dueDate ? format(new Date(bid.dueDate), "MMM d, yyyy") : "No due date"}
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    <Clock className="h-12 w-12 mx-auto mb-2 opacity-20" />
                    <p>No upcoming deadlines</p>
                  </div>
                )}
                <div className="mt-3 pt-3 border-t">
                  <Link
                    href="/bids"
                    className="text-sm flex items-center text-primary hover:underline"
                  >
                    View all bids
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Bid Trend Chart */}
            <BidTrend data={stats.bidsByMonth} />

            {/* Status Distribution Chart */}
            <StatusDistribution data={stats.bidsByStatus} />
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest actions and updates on bids
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ActivityFeed activities={stats.recentActivity} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Export the component wrapped with an error boundary
export default withErrorBoundary(Home, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Home/Dashboard page:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});