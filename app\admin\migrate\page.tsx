'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { RefreshCw } from 'lucide-react';
import { withErrorBoundary } from '@/components/error-boundary';

// Dynamically import MigrationUI with no SSR to avoid SQLite loading issues
const MigrationUI = dynamic(() => import('@/components/MigrationUI'), { 
  ssr: false,
  loading: () => (
    <div className="flex flex-col items-center justify-center p-10">
      <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
      <p className="text-lg">Loading migration tool...</p>
    </div>
  )
});

function MigratePage() {
  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-8 text-center">Database Migration</h1>
      <div className="flex justify-center">
        <MigrationUI />
      </div>
    </div>
  );
}

// Export the component wrapped with an error boundary
export default withErrorBoundary(MigratePage, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Migration page:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});