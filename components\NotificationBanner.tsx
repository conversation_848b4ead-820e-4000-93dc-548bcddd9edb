"use client";

import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, Database } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { shouldMigrateToSQLite, shouldClearIndexedDB } from '@/lib/db/utils';
import { withErrorBoundary } from '@/components/error-boundary';

export type BannerType = 'migrate' | 'clear' | 'none';

function NotificationBannerComponent() {
  const [bannerType, setBannerType] = useState<BannerType>('none');
  const [dismissed, setDismissed] = useState(false);

  // Check if we should show migration or clear banner
  useEffect(() => {
    const checkBannerType = async () => {
      try {
        // Check if banner has been dismissed in this session
        const sessionDismissed = sessionStorage.getItem('dbBannerDismissed');
        if (sessionDismissed) {
          setDismissed(true);
          return;
        }
        
        // First check if migration is needed
        const shouldMigrate = await shouldMigrateToSQLite();
        if (shouldMigrate) {
          setBannerType('migrate');
          return;
        }
        
        // Then check if clearing is needed
        const shouldClear = await shouldClearIndexedDB();
        if (shouldClear) {
          setBannerType('clear');
          return;
        }
        
        setBannerType('none');
      } catch (err) {
        console.error('Error checking banner type:', err);
        setBannerType('none');
      }
    };
    
    checkBannerType();
  }, []);
  
  // Don't render anything if nothing to show or dismissed
  if (bannerType === 'none' || dismissed) {
    return null;
  }
  
  const handleDismiss = () => {
    setDismissed(true);
    try {
      sessionStorage.setItem('dbBannerDismissed', 'true');
    } catch (e) {
      // Ignore errors if sessionStorage is not available
    }
  };
  
  return (
    <div className="fixed bottom-4 right-4 max-w-md z-50 bg-blue-50 dark:bg-blue-950 p-4 rounded-lg shadow-lg border border-blue-200 dark:border-blue-800">
      <div className="flex items-start">
        {bannerType === 'migrate' ? (
          <AlertTriangle className="h-5 w-5 text-amber-500 mr-3 mt-0.5" />
        ) : (
          <Database className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
        )}
        
        <div className="flex-1">
          <h3 className="font-medium text-blue-800 dark:text-blue-300">
            {bannerType === 'migrate' 
              ? 'Database Migration Recommended' 
              : 'IndexedDB Cleanup Recommended'}
          </h3>
          <p className="text-sm text-blue-700 dark:text-blue-400 mt-1">
            {bannerType === 'migrate'
              ? 'We\'ve switched to SQLite for better performance. Use our migration tool to transfer your existing data.'
              : 'You can now clear your IndexedDB data since SQLite is being used for storage.'}
          </p>
          <div className="mt-3 flex gap-2">
            <Link href="/admin/dashboard">
              <Button variant="outline" size="sm">
                Go to Dashboard
              </Button>
            </Link>
            
            {bannerType === 'migrate' && (
              <Link href="/admin/migrate">
                <Button size="sm">
                  Migrate Data
                </Button>
              </Link>
            )}
          </div>
        </div>
        
        <button
          onClick={handleDismiss}
          className="text-blue-400 hover:text-blue-600 dark:text-blue-300 dark:hover:text-blue-100"
          aria-label="Dismiss"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}

// Export the component wrapped with an error boundary
export const NotificationBanner = withErrorBoundary(NotificationBannerComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in NotificationBanner component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full'
});

export default NotificationBanner;