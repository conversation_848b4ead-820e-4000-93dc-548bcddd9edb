"use client";

import { useEditor, EditorContent, Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Link from '@tiptap/extension-link';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import Image from '@tiptap/extension-image';
import Typography from '@tiptap/extension-typography';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import ListItem from '@tiptap/extension-list-item';
import Heading from '@tiptap/extension-heading';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import { useCallback, useEffect } from 'react';
import { withErrorBoundary } from "@/components/error-boundary";
import './tiptap-editor.css';

interface TipTapEditorProps {
  content: string;
  onChange: (content: string) => void;
  readOnly?: boolean;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
  onEditorReady?: (editor: Editor) => void;
}

function TipTapEditorComponent({
  content,
  onChange,
  readOnly = false,
  placeholder = 'Start writing your document...',
  className = '',
  autoFocus = false,
  onEditorReady,
}: TipTapEditorProps) {
  const editor = useEditor({
    extensions: [
      // Use the full StarterKit 
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3]
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: true
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: true
        }
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary underline',
        },
      }),
      TextStyle,
      Color,
      Typography,
      Image.configure({
        inline: true,
        allowBase64: true,
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        defaultAlignment: 'left',
      }),
      BulletList,
      OrderedList,
      ListItem,
      Heading,
      // Add Table Extensions
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'table-auto border-collapse',
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'border border-gray-300',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 bg-gray-100 p-2 font-bold',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 p-2',
        },
      }),
    ],
    content,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-xl focus:outline-none p-4',
        'data-testid': 'tiptap-editor',
      },
    },
    autofocus: autoFocus,
  });

  // Log when editor is initialized
  useEffect(() => {
    if (editor) {
      console.log('TipTap editor initialized with the following extensions:');
      editor.extensionManager.extensions.forEach(extension => {
        console.log(`- ${extension.name}`);
      });
      
      // Log available commands
      console.log('Available editor commands:', Object.keys(editor.commands));
    }
  }, [editor]);

  // Provide the editor instance to the parent component when ready
  useEffect(() => {
    if (editor && onEditorReady) {
      console.log('Editor instance provided to parent component');
      onEditorReady(editor);
    }
  }, [editor, onEditorReady]);

  // Update editor content when content prop changes externally
  useEffect(() => {
    if (editor && editor.getHTML() !== content) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // Auto focus handling
  useEffect(() => {
    if (editor && autoFocus && !readOnly) {
      editor.commands.focus();
    }
  }, [editor, autoFocus, readOnly]);

  // Handle toolbar commands
  const handleCommand = useCallback((command: string, value?: any) => {
    if (!editor) return;

    switch (command) {
      case 'bold':
        editor.chain().focus().toggleBold().run();
        break;
      case 'italic':
        editor.chain().focus().toggleItalic().run();
        break;
      case 'underline':
        editor.chain().focus().toggleUnderline().run();
        break;
      case 'heading1':
        editor.chain().focus().toggleHeading({ level: 1 }).run();
        break;
      case 'heading2':
        editor.chain().focus().toggleHeading({ level: 2 }).run();
        break;
      case 'heading3':
        editor.chain().focus().toggleHeading({ level: 3 }).run();
        break;
      case 'bulletList':
        editor.chain().focus().toggleBulletList().run();
        break;
      case 'orderedList':
        editor.chain().focus().toggleOrderedList().run();
        break;
      case 'alignLeft':
        editor.chain().focus().setTextAlign('left').run();
        break;
      case 'alignCenter':
        editor.chain().focus().setTextAlign('center').run();
        break;
      case 'alignRight':
        editor.chain().focus().setTextAlign('right').run();
        break;
      case 'link':
        if (value && typeof value === 'string') {
          editor.chain().focus().setLink({ href: value }).run();
        } else {
          // Open dialog or prompt for URL
          const url = prompt('Enter URL');
          if (url) {
            editor.chain().focus().setLink({ href: url }).run();
          }
        }
        break;
      case 'unlink':
        editor.chain().focus().unsetLink().run();
        break;
      case 'clear':
        editor.chain().focus().clearNodes().unsetAllMarks().run();
        break;
      // Add table commands
      case 'insertTable':
        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        break;
      case 'addColumnBefore':
        editor.chain().focus().addColumnBefore().run();
        break;
      case 'addColumnAfter':
        editor.chain().focus().addColumnAfter().run();
        break;
      case 'deleteColumn':
        editor.chain().focus().deleteColumn().run();
        break;
      case 'addRowBefore':
        editor.chain().focus().addRowBefore().run();
        break;
      case 'addRowAfter':
        editor.chain().focus().addRowAfter().run();
        break;
      case 'deleteRow':
        editor.chain().focus().deleteRow().run();
        break;
      case 'deleteTable':
        editor.chain().focus().deleteTable().run();
        break;
      case 'mergeCells':
        editor.chain().focus().mergeCells().run();
        break;
      case 'splitCell':
        editor.chain().focus().splitCell().run();
        break;
      case 'toggleHeaderColumn':
        editor.chain().focus().toggleHeaderColumn().run();
        break;
      case 'toggleHeaderRow':
        editor.chain().focus().toggleHeaderRow().run();
        break;
      case 'toggleHeaderCell':
        editor.chain().focus().toggleHeaderCell().run();
        break;
      default:
        console.warn(`Unknown command: ${command}`);
    }
  }, [editor]);

  // Check if a format is active
  const isActive = useCallback((command: string, options?: any) => {
    if (!editor) return false;

    switch (command) {
      case 'bold':
        return editor.isActive('bold');
      case 'italic':
        return editor.isActive('italic');
      case 'underline':
        return editor.isActive('underline');
      case 'heading1':
        return editor.isActive('heading', { level: 1 });
      case 'heading2':
        return editor.isActive('heading', { level: 2 });
      case 'heading3':
        return editor.isActive('heading', { level: 3 });
      case 'bulletList':
        return editor.isActive('bulletList');
      case 'orderedList':
        return editor.isActive('orderedList');
      case 'link':
        return editor.isActive('link');
      case 'table':
        return editor.isActive('table');
      case 'tableHeader':
        return editor.isActive('tableHeader');
      default:
        return false;
    }
  }, [editor]);

  return (
    <div className={`border rounded-md ${className}`}>
      <EditorContent 
        editor={editor} 
        className="min-h-[200px] max-h-[800px] overflow-y-auto"
      />
    </div>
  );
}

// Export the component wrapped with an error boundary
export const TipTapEditor = withErrorBoundary(TipTapEditorComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in TipTapEditor component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});

// Export a utils object with editor methods
export const TiptapUtils = {
  getEditor: (editor: Editor | null) => editor,
  
  executeCommand: (editor: Editor | null, command: string, value?: any) => {
    if (!editor) return;

    switch (command) {
      case 'bold':
        editor.chain().focus().toggleBold().run();
        break;
      case 'italic':
        editor.chain().focus().toggleItalic().run();
        break;
      case 'underline':
        editor.chain().focus().toggleUnderline().run();
        break;
      case 'insertTable':
        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        break;
      case 'addColumnBefore':
        editor.chain().focus().addColumnBefore().run();
        break;
      case 'addColumnAfter':
        editor.chain().focus().addColumnAfter().run();
        break;
      case 'deleteColumn':
        editor.chain().focus().deleteColumn().run();
        break;
      case 'addRowBefore':
        editor.chain().focus().addRowBefore().run();
        break;
      case 'addRowAfter':
        editor.chain().focus().addRowAfter().run();
        break;
      case 'deleteRow':
        editor.chain().focus().deleteRow().run();
        break;
      case 'deleteTable':
        editor.chain().focus().deleteTable().run();
        break;
      default:
        console.warn(`Unknown command: ${command}`);
    }
  },
  
  isActive: (editor: Editor | null, command: string, options?: any) => {
    if (!editor) return false;

    switch (command) {
      case 'bold':
        return editor.isActive('bold');
      case 'italic':
        return editor.isActive('italic');
      case 'underline':
        return editor.isActive('underline');
      case 'heading1':
        return editor.isActive('heading', { level: 1 });
      case 'heading2':
        return editor.isActive('heading', { level: 2 });
      case 'heading3':
        return editor.isActive('heading', { level: 3 });
      case 'bulletList':
        return editor.isActive('bulletList');
      case 'orderedList':
        return editor.isActive('orderedList');
      case 'link':
        return editor.isActive('link');
      case 'table':
        return editor.isActive('table');
      case 'tableHeader':
        return editor.isActive('tableHeader');
      default:
        return false;
    }
  }
};
