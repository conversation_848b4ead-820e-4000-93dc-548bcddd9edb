"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { DocumentList } from "./document-list";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/hooks/useAuth";
import { FilePlus, Loader2 } from "lucide-react";
import { withErrorBoundary } from "@/components/error-boundary";

interface DocumentsPageProps {
  bidId: string;
}

function DocumentsPageComponent({ bidId }: DocumentsPageProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newDocTitle, setNewDocTitle] = useState("");
  const [newDocDescription, setNewDocDescription] = useState("");
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();

  const handleCreateDocument = () => {
    setIsCreateDialogOpen(true);
  };

  const handleCreateDocumentSubmit = async () => {
    if (!newDocTitle.trim()) {
      toast({
        title: "Error",
        description: "Please enter a document title",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      // Generate a unique document ID
      const documentId = crypto.randomUUID();
      
      // Create a document record in IndexedDB
      const openRequest = indexedDB.open("BidManagementDB", 1);
      
      openRequest.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object stores if they don't exist
        if (!db.objectStoreNames.contains("documents")) {
          const documentStore = db.createObjectStore("documents", { keyPath: "id" });
          documentStore.createIndex("bidId", "bidId", { unique: false });
        }
        
        if (!db.objectStoreNames.contains("versions")) {
          const versionStore = db.createObjectStore("versions", { keyPath: "id" });
          versionStore.createIndex("documentId", "documentId", { unique: false });
        }
        
        if (!db.objectStoreNames.contains("comments")) {
          const commentStore = db.createObjectStore("comments", { keyPath: "id" });
          commentStore.createIndex("documentId", "documentId", { unique: false });
        }
      };
      
      openRequest.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const transaction = db.transaction("documents", "readwrite");
        const store = transaction.objectStore("documents");
        
        const now = new Date().toISOString();
        
        const document = {
          id: documentId,
          bidId,
          title: newDocTitle,
          description: newDocDescription,
          content: "", // Start with empty content
          status: "draft",
          createdAt: now,
          updatedAt: now,
          createdBy: {
            id: user?.userId || "unknown",
            name: user?.displayName || user?.userId || "Anonymous",
            role: "User"
          }
        };
        
        const request = store.add(document);
        
        request.onsuccess = () => {
          // Reset form
          setNewDocTitle("");
          setNewDocDescription("");
          setIsCreateDialogOpen(false);
          
          toast({
            title: "Document created",
            description: "Your new document has been created"
          });
          
          // Verify document was created before redirecting
          const verifyTransaction = db.transaction("documents", "readonly");
          const verifyStore = verifyTransaction.objectStore("documents");
          const verifyRequest = verifyStore.get(documentId);
          
          verifyRequest.onsuccess = () => {
            if (verifyRequest.result) {
              console.log("Document created successfully:", verifyRequest.result);
              // Document exists, safe to navigate
              setTimeout(() => {
                router.push(`/bids/${bidId}/documents/${documentId}`);
              }, 300); // Small delay to ensure IndexedDB has completed all operations
            } else {
              console.error("Document was not found after creation");
              toast({
                title: "Error",
                description: "Document was created but could not be loaded. Please refresh the documents list.",
                variant: "destructive",
              });
              setIsCreating(false);
            }
          };
          
          verifyRequest.onerror = () => {
            console.error("Error verifying document creation");
            // Navigate anyway, but there might be issues
            router.push(`/bids/${bidId}/documents/${documentId}`);
          };
        };
        
        request.onerror = (e) => {
          console.error("Error adding document:", e);
          toast({
            title: "Error",
            description: "Failed to create document",
            variant: "destructive",
          });
          setIsCreating(false);
        };
        
        transaction.oncomplete = () => {
          // Don't close the DB until the verification is done
        };
      };
      
      openRequest.onerror = (e) => {
        console.error("Error opening database:", e);
        toast({
          title: "Error",
          description: "Could not access the document database",
          variant: "destructive",
        });
        setIsCreating(false);
      };
    } catch (error) {
      console.error("Error creating document:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      setIsCreating(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <DocumentList 
        bidId={bidId} 
        onCreate={handleCreateDocument}
      />
      
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Document</DialogTitle>
            <DialogDescription>
              Add a new document to this bid. You can edit the content after creation.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Document Title</Label>
              <Input
                id="title"
                placeholder="Enter document title"
                value={newDocTitle}
                onChange={(e) => setNewDocTitle(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                placeholder="Brief description of this document"
                value={newDocDescription}
                onChange={(e) => setNewDocDescription(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateDocumentSubmit} disabled={isCreating}>
              {isCreating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <FilePlus className="mr-2 h-4 w-4" />
                  Create Document
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Export the component wrapped with an error boundary
export const DocumentsPage = withErrorBoundary(DocumentsPageComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in DocumentsPage component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});
