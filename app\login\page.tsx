"use client"

import { useState } from 'react'
import { useR<PERSON>er, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { UserCircle2, Lock } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/hooks/useAuth'
import { withErrorBoundary } from '@/components/error-boundary'

function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login } = useAuth()
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const formData = new FormData(e.currentTarget)
      const userName = formData.get('username') as string
      const password = formData.get('password') as string

      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userName, password }),
      })

      const data = await response.json()

      if (data.status === 'success' && data.data) {
        // Update auth state
        login(data.data)

        // Show success toast and wait a bit for it to be visible
        toast({
          title: "Success",
          description: "Successfully logged in",
          duration: 3000,
        })
        await new Promise(resolve => setTimeout(resolve, 500))

        // Then redirect to home page
        router.push('/')
      } else {
        // Handle error response
        throw new Error(data.message || 'Authentication failed')
      }
    } catch (error: any) {
      console.error('Login error:', error)
      toast({
        variant: "destructive",
        title: "Authentication Error",
        description: error.message || 'Failed to login. Please try again.',
        duration: 5000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-[calc(100vh-4rem)] flex items-center justify-center py-12">
      <div className="container mx-auto px-4 max-w-md">
        <Card className="gradient-border">
          <CardHeader className="space-y-4 text-center">
            <div className="w-16 h-16 mx-auto rounded-full gradient-bg flex items-center justify-center">
              <UserCircle2 className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-2xl">Welcome Back</CardTitle>
            <CardDescription>
              Sign in to access government services
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <div className="relative">
                  <UserCircle2 className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="username"
                    placeholder="Enter your username"
                    className="pl-9"
                    name="username"
                    required
                    disabled={isLoading}
                    autoComplete="username"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    className="pl-9"
                    name="password"
                    required
                    disabled={isLoading}
                    autoComplete="current-password"
                  />
                </div>
              </div>
              <Button 
                type="submit" 
                className="w-full gradient-bg hover:opacity-90 transition-opacity"
                disabled={isLoading}
                aria-busy={isLoading}
              >
                <span className="relative">
                  {isLoading ? 'Signing in...' : 'Sign In'}
                </span>
              </Button>
              <div className="mt-4 text-center">
                <Button
                  type="button"
                  variant="link"
                  className="text-sm text-primary hover:underline"
                  onClick={() => {
                    const usernameInput = document.querySelector('#username') as HTMLInputElement;
                    const passwordInput = document.querySelector('#password') as HTMLInputElement;
                    
                    usernameInput.value = "JamesPeter";
                    passwordInput.value = "DCXDBSapp@03";
                  }}
                  disabled={isLoading}
                >
                  Quick Login
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Export the component wrapped with an error boundary
export default withErrorBoundary(LoginPage, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Login page:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});