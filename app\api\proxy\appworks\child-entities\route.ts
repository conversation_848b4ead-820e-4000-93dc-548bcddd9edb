export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { cookies } from 'next/headers';

/**
 * This is a proxy API endpoint to handle Appworks child entity list API requests
 * It works around CORS issues by proxying the request from our server
 * 
 * Expected query parameters:
 * - parentEntityType: The parent entity type (e.g., 'Bid')
 * - childEntityType: The child entity type (e.g., 'LifecycleTask')
 * - listName: The list name (e.g., 'BidDefaultTaskList')
 */
export async function POST(request: NextRequest) {
  try {
    // Get the entity types and list name from query params
    const searchParams = request.nextUrl.searchParams;
    const parentEntityType = searchParams.get('parentEntityType');
    const childEntityType = searchParams.get('childEntityType');
    const listName = searchParams.get('listName') || 'DefaultList';
    
    if (!parentEntityType || !childEntityType) {
      return NextResponse.json({ 
        error: 'Missing required parameters: parentEntityType and childEntityType' 
      }, { status: 400 });
    }
    
    // Extract the request body
    const requestBody = await request.json();
    
    // Get authorization token from cookies or headers
    const cookieStore = cookies();
    const sessionCookie = cookieStore.get('appSession');
    const authHeader = request.headers.get('Authorization');
    
    let samlToken = '';
    
    // First try to get token from Authorization header
    if (authHeader && authHeader.startsWith('Bearer ')) {
      samlToken = authHeader.substring(7);
    } 
    // If not in header, try to get from cookie
    else if (sessionCookie?.value) {
      try {
        const sessionData = JSON.parse(sessionCookie.value);
        if (sessionData?.samlArtifact) {
          samlToken = sessionData.samlArtifact;
        }
      } catch (e) {
        console.error('Error parsing session cookie:', e);
      }
    }
    
    if (!samlToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Construct the Appworks API URL for child entities
    const apiUrl = `${process.env.NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/${parentEntityType}/childEntities/${childEntityType}/lists/${listName}`;
    
    console.log(`Proxying child entity request to Appworks: ${apiUrl}`);
    console.log('Request body:', JSON.stringify(requestBody, null, 2));
    
    // Call the Appworks API
    const response = await axios.post(
      apiUrl,
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'SAMLart': samlToken
        },
        timeout: 30000 // 30 seconds timeout
      }
    );
    
    console.log('Appworks response status:', response.status);
    console.log('Appworks response data preview:', {
      page: response.data?.page,
      linksCount: Object.keys(response.data?._links || {}).length,
      embeddedKeys: Object.keys(response.data?._embedded || {}),
      itemCount: response.data?._embedded?.[listName]?.length || 0
    });
    
    // Return the Appworks response
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('Error in Appworks child entity proxy API:', error);
    
    // Return a more detailed error response
    return NextResponse.json(
      { 
        error: 'Failed to retrieve child entity data from Appworks',
        details: error.message,
        status: error.response?.status,
        data: error.response?.data
      },
      { status: error.response?.status || 500 }
    );
  }
}
