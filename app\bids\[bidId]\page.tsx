import { Bid } from "@/types";
import BidDetailsClient from "./bid-details-client";
import { withErrorBoundary } from "@/components/error-boundary";

// Client component that fetches data and renders the bid details
function BidDetailsPage({
  params,
}: {
  params: { bidId: string };
}) {
  return <BidDetailsClient bidId={params.bidId} />;
}

// Export the component wrapped with an error boundary
export default withErrorBoundary(BidDetailsPage, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Bid Details page:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});