import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { AuthProvider } from '@/hooks/useAuth';
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from '@/components/theme-provider';
import dynamic from 'next/dynamic';
import { ErrorBoundary } from '@/components/error-boundary';
import { ErrorInfo } from 'react';

// Import the notification banner with no SSR to avoid hydration issues
const NotificationBanner = dynamic(
  () => import('@/components/NotificationBanner'),
  { ssr: false }
);

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'BidManager - Manage Your Bids Efficiently',
  description: 'A comprehensive platform for managing and tracking bids',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} min-h-screen flex flex-col`}>
        <ThemeProvider defaultTheme="light" storageKey="bidmanager-theme">
          <ErrorBoundary 
            onError={(error: Error, errorInfo: React.ErrorInfo) => {
              // Log to your preferred error monitoring service
              console.error('Root layout error:', error, errorInfo);
            }}
          >
            <AuthProvider>
              <Header />
              <main className="flex-1">
                {children}
              </main>
              <Footer />
              <Toaster />
              <NotificationBanner />
            </AuthProvider>
          </ErrorBoundary>
        </ThemeProvider>
      </body>
    </html>
  );
}