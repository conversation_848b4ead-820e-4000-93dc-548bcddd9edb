// Run tests directly using Jest API
const { runCLI } = require('@jest/core');

async function runTests() {
  try {
    console.log('Running SQLiteDB tests with Jest API...');
    
    const jestConfig = {
      roots: ['<rootDir>/__tests__'],
      testMatch: ['**/__tests__/sqlitedb.test.ts'],
      verbose: true,
      silent: false,
      testPathIgnorePatterns: [],
      collectCoverage: false,
    };
    
    const { results } = await runCLI(jestConfig, [process.cwd()]);
    
    console.log('\n=== TEST RESULTS ===');
    console.log(`Tests: ${results.numTotalTests}`);
    console.log(`Passing: ${results.numPassedTests}`);
    console.log(`Failing: ${results.numFailedTests}`);
    
    if (results.numFailedTests > 0) {
      console.log('\n=== FAILED TESTS ===');
      results.testResults.forEach(testResult => {
        if (testResult.numFailingTests > 0) {
          console.log(`\nFile: ${testResult.testFilePath}`);
          testResult.testResults.forEach(test => {
            if (test.status === 'failed') {
              console.log(`- ${test.fullName}: ${test.status}`);
              console.log(`  ${test.failureMessages.join('\n  ')}`);
            }
          });
        }
      });
    }
    
    console.log('\nTests completed!');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

runTests();
