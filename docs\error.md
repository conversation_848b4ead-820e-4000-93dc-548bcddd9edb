npm test -- sqlitedb

> nextjs@0.1.0 test
> jest sqlitedb

 FAIL  __tests__/sqlitedb.test.js
  ● Console

    console.log
      SQLite database initialized successfully

      at log (lib/sqlitedb.ts:129:13)

    console.log
      Saving document to SQLite: {
        id: 'doc-test-1',
        bidId: 'bid-test-1',
        title: 'Test Document',
        content: 'This is test content',
        createdAt: '2025-06-22T12:15:50.864Z',
        updatedAt: '2025-06-22T12:15:50.864Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)

    console.log
      Document doc-test-1 saved successfully

      at log (lib/sqlitedb.ts:264:13)

    console.log
      Saving document to SQLite: {
        id: 'doc-test-bid-1',
        bidId: 'bid-test-batch',
        title: 'Doc 1',
        content: 'Content 1',
        createdAt: '2025-06-22T12:15:50.869Z',
        updatedAt: '2025-06-22T12:15:50.869Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)
          at Array.map (<anonymous>)

    console.log
      Document doc-test-bid-1 saved successfully

      at log (lib/sqlitedb.ts:264:13)
          at Array.map (<anonymous>)

    console.log
      Saving document to SQLite: {
        id: 'doc-test-bid-2',
        bidId: 'bid-test-batch',
        title: 'Doc 2',
        content: 'Content 2',
        createdAt: '2025-06-22T12:15:50.869Z',
        updatedAt: '2025-06-22T12:15:50.869Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)
          at Array.map (<anonymous>)

    console.log
      Document doc-test-bid-2 saved successfully

      at log (lib/sqlitedb.ts:264:13)
          at Array.map (<anonymous>)

    console.log
      SQLite database closed

      at log (lib/sqlitedb.ts:144:13)

  ● SQLite Database › Document Operations › should save and retrieve a document
                                                                                                                                                                                                                       
    expect(received).not.toBeNull()                                                                                                                                                                                    
                                                                                                                                                                                                                       
    Received: null                                                                                                                                                                                                     
                                                                                                                                                                                                                       
      61 |                                                                                                                                                                                                             
      62 |       // Verify document
    > 63 |       expect(retrievedDoc).not.toBeNull();
         |                                ^
      64 |       expect(retrievedDoc?.id).toBe(testDoc.id);
      65 |       expect(retrievedDoc?.bidId).toBe(testDoc.bidId);
      66 |       expect(retrievedDoc?.title).toBe(testDoc.title);

      at Object.<anonymous> (__tests__/sqlitedb.test.js:63:32)

  ● SQLite Database › Document Operations › should retrieve documents by bid ID

    expect(received).toBe(expected) // Object.is equality

    Expected: 2
    Received: 0

      101 |
      102 |       // Verify retrieval
    > 103 |       expect(retrievedDocs.length).toBe(2);
          |                                    ^
      104 |       expect(retrievedDocs.map(d => d.id).sort()).toEqual(['doc-test-bid-1', 'doc-test-bid-2'].sort());
      105 |     });
      106 |   });

      at Object.<anonymous> (__tests__/sqlitedb.test.js:103:36)

 FAIL  __tests__/sqlitedb.test.ts
  ● Console

    console.log
      SQLite database initialized successfully

      at log (lib/sqlitedb.ts:129:13)

    console.log
      Saving document to SQLite: {
        id: 'doc-test-1',
        bidId: 'bid-test-1',
        title: 'Test Document',
        content: 'This is test content',
        createdAt: '2025-06-22T12:15:51.000Z',
        updatedAt: '2025-06-22T12:15:51.000Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)

    console.log
      Document doc-test-1 saved successfully

      at log (lib/sqlitedb.ts:264:13)

    console.log
      Saving document to SQLite: {
        id: 'doc-test-bid-1',
        bidId: 'bid-test-batch',
        title: 'Doc 1',
        content: 'Content 1',
        createdAt: '2025-06-22T12:15:51.005Z',
        updatedAt: '2025-06-22T12:15:51.005Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)
          at Array.map (<anonymous>)

    console.log
      Document doc-test-bid-1 saved successfully

      at log (lib/sqlitedb.ts:264:13)
          at Array.map (<anonymous>)

    console.log
      Saving document to SQLite: {
        id: 'doc-test-bid-2',
        bidId: 'bid-test-batch',
        title: 'Doc 2',
        content: 'Content 2',
        createdAt: '2025-06-22T12:15:51.005Z',
        updatedAt: '2025-06-22T12:15:51.005Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)
          at Array.map (<anonymous>)

    console.log
      Document doc-test-bid-2 saved successfully

      at log (lib/sqlitedb.ts:264:13)
          at Array.map (<anonymous>)

    console.log
      Saving document to SQLite: {
        id: 'doc-versions-test',
        bidId: 'bid-test',
        title: 'Version Test Doc',
        content: 'Initial content',
        createdAt: '2025-06-22T12:15:51.008Z',
        updatedAt: '2025-06-22T12:15:51.008Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)

    console.log
      Document doc-versions-test saved successfully

      at log (lib/sqlitedb.ts:264:13)

    console.log
      Document version version-test-1 saved successfully

      at log (lib/sqlitedb.ts:424:13)

    console.log
      Saving document to SQLite: {
        id: 'doc-latest-version-test',
        bidId: 'bid-latest-test',
        title: 'Latest Version Test',
        content: 'Content',
        createdAt: '2025-06-22T12:15:51.022Z',
        updatedAt: '2025-06-22T12:15:51.022Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)

    console.log
      Document doc-latest-version-test saved successfully

      at log (lib/sqlitedb.ts:264:13)

    console.log
      Document version v1 saved successfully

      at log (lib/sqlitedb.ts:424:13)

    console.log
      Document version v2 saved successfully

      at log (lib/sqlitedb.ts:424:13)

    console.log
      Saving document to SQLite: {
        id: 'doc-comments-test',
        bidId: 'bid-comments-test',
        title: 'Comments Test Doc',
        content: 'Content for comment testing',
        createdAt: '2025-06-22T12:15:51.027Z',
        updatedAt: '2025-06-22T12:15:51.027Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)

    console.log
      Document doc-comments-test saved successfully

      at log (lib/sqlitedb.ts:264:13)

    console.log
      Document comment comment-1 saved successfully

      at log (lib/sqlitedb.ts:652:13)
          at Array.map (<anonymous>)

    console.log
      Document comment comment-2 saved successfully

      at log (lib/sqlitedb.ts:652:13)
          at Array.map (<anonymous>)

    console.log
      Saving document to SQLite: {
        id: 'doc-reply-test',
        bidId: 'bid-reply-test',
        title: 'Reply Test Doc',
        content: 'Content for testing replies',
        createdAt: '2025-06-22T12:15:51.030Z',
        updatedAt: '2025-06-22T12:15:51.030Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)

    console.log
      Document doc-reply-test saved successfully

      at log (lib/sqlitedb.ts:264:13)

    console.log
      Document comment parent-comment saved successfully

      at log (lib/sqlitedb.ts:652:13)

    console.log
      Document comment reply-comment saved successfully

      at log (lib/sqlitedb.ts:652:13)

    console.log
      Saving document to SQLite: {
        id: 'doc-edits-test',
        bidId: 'bid-edits-test',
        title: 'Edits Test Doc',
        content: 'Initial content',
        createdAt: '2025-06-22T12:15:51.031Z',
        updatedAt: '2025-06-22T12:15:51.031Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)

    console.log
      Document doc-edits-test saved successfully

      at log (lib/sqlitedb.ts:264:13)

    console.log
      Document edit edit-test-1 saved successfully

      at log (lib/sqlitedb.ts:795:13)

    console.log
      Saving document to SQLite: {
        id: 'doc-clear-edits-test',
        bidId: 'bid-clear-edits-test',
        title: 'Clear Edits Test',
        content: 'Content',
        createdAt: '2025-06-22T12:15:51.033Z',
        updatedAt: '2025-06-22T12:15:51.033Z',
        createdBy: 'test-user',
        status: 'draft',
        lastVersionId: null,
        lastVersion: null,
        metadata: {}
      }

      at log (lib/sqlitedb.ts:239:13)

    console.log
      Document doc-clear-edits-test saved successfully

      at log (lib/sqlitedb.ts:264:13)

    console.log
      Document edit edit-1 saved successfully

      at log (lib/sqlitedb.ts:795:13)
          at Array.map (<anonymous>)

    console.log
      Document edit edit-2 saved successfully

      at log (lib/sqlitedb.ts:795:13)
          at Array.map (<anonymous>)

    console.log
      Document edits for doc-clear-edits-test cleared successfully

      at log (lib/sqlitedb.ts:842:13)

    console.log
      SQLite database closed

      at log (lib/sqlitedb.ts:144:13)

  ● SQLite Database › Document Versions › should save and retrieve document versions
                                                                                                                                                                                                                       
    expect(received).toBe(expected) // Object.is equality                                                                                                                                                              
                                                                                                                                                                                                                       
    Expected: "user-1"                                                                                                                                                                                                 
    Received: undefined                                                                                                                                                                                                

      193 |       if (retrievedVersion) {
      194 |         // @ts-ignore - Access the fields that exist in the SQLite version but not in the type
    > 195 |         expect(retrievedVersion.createdBy_id).toBe('user-1');
          |                                               ^
      196 |         // @ts-ignore
      197 |         expect(retrievedVersion.createdBy_name).toBe('Test User');
      198 |         // @ts-ignore

      at toBe (__tests__/sqlitedb.test.ts:195:47)
      at call (__tests__/sqlitedb.test.ts:2:1)
      at Generator.tryCatch (__tests__/sqlitedb.test.ts:2:1)
      at Generator._invoke [as next] (__tests__/sqlitedb.test.ts:2:1)
      at asyncGeneratorStep (__tests__/sqlitedb.test.ts:2:1)
      at asyncGeneratorStep (__tests__/sqlitedb.test.ts:2:1)

  ● SQLite Database › Document Versions › should get latest version

    expect(received).toBe(expected) // Object.is equality

    Expected: "v2"
    Received: "v1"

      263 |       // Verify it's the correct one
      264 |       expect(latestVersion).not.toBeNull();
    > 265 |       expect(latestVersion?.id).toBe('v2');
          |                                 ^
      266 |       expect(latestVersion?.version).toBe('2.0');
      267 |     });
      268 |   });

      at toBe (__tests__/sqlitedb.test.ts:265:33)
      at call (__tests__/sqlitedb.test.ts:2:1)
      at Generator.tryCatch (__tests__/sqlitedb.test.ts:2:1)
      at Generator._invoke [as next] (__tests__/sqlitedb.test.ts:2:1)
      at asyncGeneratorStep (__tests__/sqlitedb.test.ts:2:1)
      at asyncGeneratorStep (__tests__/sqlitedb.test.ts:2:1)

  ● SQLite Database › Document Comments › should save and retrieve comments

    expect(received).toBe(expected) // Object.is equality

    Expected: "User One"
    Received: undefined

      333 |       expect(comment1).not.toBeUndefined();
      334 |       expect(comment1?.content).toBe('This is comment 1');
    > 335 |       expect(comment1?.author.name).toBe('User One');
          |                                     ^
      336 |
      337 |       // Delete a comment - simulate by removing from mock storage
      338 |       delete global.__dbStorage.documentComments['comment-1'];

      at toBe (__tests__/sqlitedb.test.ts:335:37)
      at call (__tests__/sqlitedb.test.ts:2:1)
      at Generator.tryCatch (__tests__/sqlitedb.test.ts:2:1)
      at Generator._invoke [as next] (__tests__/sqlitedb.test.ts:2:1)
      at asyncGeneratorStep (__tests__/sqlitedb.test.ts:2:1)
      at asyncGeneratorStep (__tests__/sqlitedb.test.ts:2:1)

  ● SQLite Database › Document Comments › should handle comment replies

    expect(received).toBe(expected) // Object.is equality

    Expected: "parent-comment"
    Received: undefined

      417 |
      418 |       // @ts-ignore - Access the field that exists in the SQLite result but not in the type
    > 419 |       expect(replyComment1?.parent_comment_id).toBe(parentId);
          |                                                ^
      420 |     });
      421 |   });
      422 |

      at toBe (__tests__/sqlitedb.test.ts:419:48)
      at call (__tests__/sqlitedb.test.ts:2:1)
      at Generator.tryCatch (__tests__/sqlitedb.test.ts:2:1)
      at Generator._invoke [as next] (__tests__/sqlitedb.test.ts:2:1)
      at asyncGeneratorStep (__tests__/sqlitedb.test.ts:2:1)
      at asyncGeneratorStep (__tests__/sqlitedb.test.ts:2:1)

Test Suites: 2 failed, 2 total
Tests:       6 failed, 4 passed, 10 total
Snapshots:   0 total
Time:        3.105 s
Ran all test suites matching /sqlitedb/i.