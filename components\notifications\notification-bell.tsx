'use client';

import { useState, useEffect, useCallback } from 'react';
import { Bell } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from '@/components/ui/button';
import { NotificationList } from './notification-list';
import { cn } from '@/lib/utils';
import { withErrorBoundary } from '@/components/error-boundary';

interface NotificationBellProps {
  vendorId: string;
  className?: string;
}

function NotificationBellComponent({ vendorId, className }: NotificationBellProps) {
  const [unreadCount, setUnreadCount] = useState(0);
  const [open, setOpen] = useState(false);

  const fetchUnreadCount = useCallback(async () => {
    try {
      const response = await fetch(`/api/notifications/unread-count?vendorId=${vendorId}`);
      if (response.ok) {
        const data = await response.json();
        setUnreadCount(data.count);
      }
    } catch (error) {
      console.error("Error fetching unread count:", error);
    }
  }, [vendorId]);

  useEffect(() => {
    fetchUnreadCount();
    // Poll for new notifications every minute
    const interval = setInterval(fetchUnreadCount, 60000);
    return () => clearInterval(interval);
  }, [vendorId, fetchUnreadCount]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className={cn("relative", className)}
        >
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-destructive-foreground">
              {unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h4 className="text-sm font-medium">Notifications</h4>
          {unreadCount > 0 && (
            <span className="text-xs text-gray-500">
              {unreadCount} unread
            </span>
          )}
        </div>
        <NotificationList
          vendorId={vendorId}
          onNotificationClick={() => {
            // Close the popover when a notification is clicked
            setOpen(false);
            // Update the unread count
            fetchUnreadCount();
          }}
        />
      </PopoverContent>
    </Popover>
  );
}

// Export the component wrapped with an error boundary
export const NotificationBell = withErrorBoundary(NotificationBellComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in NotificationBell component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full'
});
