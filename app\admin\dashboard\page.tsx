'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { DBSwitcher } from "@/components/db-switcher";
import { ClearIndexedDB } from "@/components/ClearIndexedDB";
import { DBProvider } from "@/providers/db-provider";
import Link from "next/link";
import * as migration from "@/lib/db/migration";
import * as IndexedDB from "@/lib/indexeddb";
import { Database, HardDrive, ArrowRightLeft, RefreshCw, CheckCircle, XCircle } from 'lucide-react';
import { withErrorBoundary } from "@/components/error-boundary";

interface DatabaseStats {
  documents: number;
  versions: number;
  comments: number;
  edits: number;
}

function Dashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [indexedDBStats, setIndexedDBStats] = useState<DatabaseStats | null>(null);
  const [sqliteStats, setSqliteStats] = useState<DatabaseStats | null>(null);
  const [indexedDBAvailable, setIndexedDBAvailable] = useState(false);
  const [sqliteAvailable, setSqliteAvailable] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sqlitedb, setSqlitedb] = useState<any>(null);

  // Load SQLite module on client-side only
  useEffect(() => {
    async function loadSQLite() {
      try {
        // Dynamic import for client-side only
        const sqliteModule = await import('@/lib/sqlitedb');
        setSqlitedb(sqliteModule.default);
      } catch (err: any) {
        console.error('Error loading SQLite module:', err);
        setError(err.message || 'Failed to load SQLite module');
      }
    }
    
    loadSQLite();
  }, []);

  // Then, load database stats once SQLite is available
  useEffect(() => {
    if (!sqlitedb) return;

    async function loadDatabaseStats() {
      try {
        setIsLoading(true);
        setError(null);

        // Check database availability
        const idbAvailable = IndexedDB.isIndexedDBAvailable();
        setIndexedDBAvailable(idbAvailable);

        try {
          await sqlitedb.initSQLite();
          const sqliteAvailable = sqlitedb.isSQLiteAvailable();
          setSqliteAvailable(sqliteAvailable);
        } catch (sqliteErr) {
          console.error('SQLite initialization error:', sqliteErr);
          setSqliteAvailable(false);
        }

        // Get database statistics
        if (idbAvailable) {
          try {
            const validation = await migration.validateMigration();
            if (validation.details?.indexeddb) {
              setIndexedDBStats(validation.details.indexeddb);
            }
            if (validation.details?.sqlite) {
              setSqliteStats(validation.details.sqlite);
            }
          } catch (validationErr) {
            console.error('Validation error:', validationErr);
            // If validation fails but databases are available, set empty stats
            if (idbAvailable && !indexedDBStats) {
              setIndexedDBStats({ documents: 0, versions: 0, comments: 0, edits: 0 });
            }
            if (sqliteAvailable && !sqliteStats) {
              setSqliteStats({ documents: 0, versions: 0, comments: 0, edits: 0 });
            }
          }
        }
      } catch (err: any) {
        console.error('Error loading database stats:', err);
        setError(err.message || 'An error occurred while loading database statistics');
      } finally {
        setIsLoading(false);
      }
    }

    loadDatabaseStats();
  }, [sqlitedb]);

  const refreshStats = async () => {
    if (!sqlitedb) {
      setError("SQLite module is not loaded yet");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      try {
        const validation = await migration.validateMigration();
        if (validation.details?.indexeddb) {
          setIndexedDBStats(validation.details.indexeddb);
        }
        if (validation.details?.sqlite) {
          setSqliteStats(validation.details.sqlite);
        }
      } catch (err) {
        console.error("Error during validation:", err);
        // If validation fails, try to at least show that the DBs are available
        if (indexedDBAvailable && !indexedDBStats) {
          setIndexedDBStats({ documents: 0, versions: 0, comments: 0, edits: 0 });
        }
        if (sqliteAvailable && !sqliteStats) {
          setSqliteStats({ documents: 0, versions: 0, comments: 0, edits: 0 });
        }
      }
    } catch (err: any) {
      console.error('Error refreshing stats:', err);
      setError(err.message || 'An error occurred while refreshing database statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStatsCard = (title: string, stats: DatabaseStats | null, available: boolean) => {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {title === 'IndexedDB' ? (
              <HardDrive className="h-5 w-5" />
            ) : (
              <Database className="h-5 w-5" />
            )}
            {title}
          </CardTitle>
          <CardDescription>
            Database statistics and status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!available ? (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertTitle>Not Available</AlertTitle>
              <AlertDescription>
                {title} is not available in this environment.
              </AlertDescription>
            </Alert>
          ) : stats ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Documents</p>
                  <p className="text-2xl font-bold">{stats.documents}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Versions</p>
                  <p className="text-2xl font-bold">{stats.versions}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Comments</p>
                  <p className="text-2xl font-bold">{stats.comments}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Edits</p>
                  <p className="text-2xl font-bold">{stats.edits}</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-5 w-5 animate-spin text-muted-foreground" />
              <span className="ml-2 text-muted-foreground">Loading stats...</span>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Badge variant={available ? "default" : "destructive"}>
            {available ? "Available" : "Unavailable"}
          </Badge>
          {available && stats && (
            <p className="text-sm text-muted-foreground">
              Total Records: {stats.documents + stats.versions + stats.comments + stats.edits}
            </p>
          )}
        </CardFooter>
      </Card>
    );
  };

  // If SQLite module is not loaded yet, show loading
  if (!sqlitedb) {
    return (
      <div className="container py-10 flex flex-col items-center justify-center">
        <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
        <h2 className="text-xl font-semibold">Loading SQLite module...</h2>
      </div>
    );
  }

  return (
    <DBProvider>
      <div className="container py-10 max-w-6xl">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Database Dashboard</h1>
          <Button onClick={refreshStats} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {renderStatsCard("IndexedDB", indexedDBStats, indexedDBAvailable)}
          {renderStatsCard("SQLite", sqliteStats, sqliteAvailable)}
        </div>

        <Separator className="my-8" />

        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Database Tools</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <DBSwitcher />
            <ClearIndexedDB />
          </div>
          
          <div className="mt-8 bg-blue-50 dark:bg-blue-950 p-6 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-semibold mb-2 text-blue-700 dark:text-blue-300 flex items-center">
              <Database className="mr-2 h-5 w-5" />
              SQLite is now the default database
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              All new documents will be stored in SQLite by default. This provides better performance and reliability.
              You can use the tools above to switch database providers or clear old IndexedDB data.
            </p>
          </div>
          
          <div className="mt-6 text-center">
            <Link href="/admin/migrate">
              <Button variant="outline">
                <ArrowRightLeft className="mr-2 h-4 w-4" />
                Go to Migration Page
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </DBProvider>
  );
}

// Export the component wrapped with an error boundary
export default withErrorBoundary(Dashboard, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Admin Dashboard page:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});