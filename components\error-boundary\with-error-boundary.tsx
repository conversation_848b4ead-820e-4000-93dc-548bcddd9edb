import React from 'react';
import { ErrorBoundary } from './error-boundary';
import { useErrorLogger } from '@/hooks/useErrorLogger';

interface WithErrorBoundaryOptions {
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  className?: string;
}

/**
 * Higher-order component that wraps a component with an error boundary
 * 
 * @param Component The component to wrap
 * @param options Configuration options for the error boundary
 * @returns A new component wrapped with an error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  options: WithErrorBoundaryOptions = {}
) {
  const { fallback, onError, className } = options;
  const displayName = Component.displayName || Component.name || 'Component';

  function WithErrorBoundary(props: P) {
    const { logError } = useErrorLogger();

    const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
      // Log the error using our custom hook
      logError(error, errorInfo);
      
      // Call the custom onError handler if provided
      if (onError) {
        onError(error, errorInfo);
      }
    };

    return (
      <ErrorBoundary 
        fallback={fallback}
        onError={handleError}
        className={className}
      >
        <Component {...props} />
      </ErrorBoundary>
    );
  }

  WithErrorBoundary.displayName = `WithErrorBoundary(${displayName})`;
  
  return WithErrorBoundary;
}
