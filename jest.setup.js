// Learn more: https://github.com/testing-library/jest-dom
require('@testing-library/jest-dom');

// Mock sql.js with state persistence

// Create in-memory storage for our mock database
global.dbStorage = {
  documents: {},
  documentVersions: {},
  documentComments: {},
  documentEdits: {}
};

// Helper to create a stateful prepared statement
const createMockStatement = (sql) => {
  // Parse the SQL to determine operation type
  const isInsert = sql.toLowerCase().includes('insert into');
  const isSelect = sql.toLowerCase().includes('select');
  const isDelete = sql.toLowerCase().includes('delete from');
  
  // Extract table name if possible
  let tableName = null;
  if (isInsert) {
    const match = sql.match(/insert into\s+([\w_]+)/i);
    tableName = match ? match[1] : null;
  } else if (isSelect) {
    const match = sql.match(/from\s+([\w_]+)/i);
    tableName = match ? match[1] : null;
  } else if (isDelete) {
    const match = sql.match(/delete from\s+([\w_]+)/i);
    tableName = match ? match[1] : null;
  }
  
  // Check if this is a select by ID query
  const whereIdMatch = sql.toLowerCase().match(/where\s+id\s*=\s*\?/i);
  const isSelectById = isSelect && whereIdMatch;
  
  // Check if this is a select by document ID query
  const whereDocIdMatch = sql.toLowerCase().match(/where\s+document_?id\s*=\s*\?/i);
  const isSelectByDocumentId = isSelect && whereDocIdMatch;
  
  // State for the statement
  let bindParams = [];
  let hasExecuted = false;
  let results = [];
  let currentIndex = 0;
  
  const statement = {
    bind: function(params) {
      bindParams = params || [];
      return this;
    },
    
    step: function() {
      // If we've already processed all results, return false
      if (currentIndex >= results.length && hasExecuted) {
        return false;
      }
      
      // If we haven't executed yet, process the query
      if (!hasExecuted) {
        hasExecuted = true;
        
        // Handle different query types
        if (isSelectById && tableName === 'documents') {
          const docId = bindParams[0];
          const doc = dbStorage.documents[docId];
          if (doc) {
            results = [JSON.parse(JSON.stringify(doc))];
          }
        } 
        else if (isSelectById && tableName === 'document_versions') {
          const versionId = bindParams[0];
          const version = dbStorage.documentVersions[versionId];
          if (version) {
            const versionCopy = JSON.parse(JSON.stringify(version));
            
            // Ensure SQLite-specific fields are present
            if (version.metadata?.createdBy) {
              versionCopy.createdBy_id = version.metadata.createdBy.id || version.createdBy_id || 'unknown';
              versionCopy.createdBy_name = version.metadata.createdBy.name || version.createdBy_name || 'Unknown User';
              versionCopy.createdBy_role = version.metadata.createdBy.role || version.createdBy_role || 'user';
            }
            
            // Add commitMessage if available in metadata
            if (version.metadata?.commitMessage) {
              versionCopy.commitMessage = version.metadata.commitMessage;
            }
            
            // Add status from metadata if available
            if (version.metadata?.status) {
              versionCopy.status = version.metadata.status;
            } else {
              versionCopy.status = 'draft';
            }
            
            // Add createdAt from metadata if available
            if (version.metadata?.createdAt) {
              versionCopy.createdAt = version.metadata.createdAt;
            }
            
            results = [versionCopy];
          }
        }
        else if (isSelectById && tableName === 'document_comments') {
          const commentId = bindParams[0];
          const comment = dbStorage.documentComments[commentId];
          if (comment) {
            const commentCopy = JSON.parse(JSON.stringify(comment));
            
            // Map author fields to SQLite format
            if (comment.author) {
              commentCopy.author_id = comment.author.id;
              commentCopy.author_name = comment.author.name;
              commentCopy.author_avatar = comment.author.avatar || null;
            }
            
            // Ensure parent_comment_id is properly set
            if (comment.parent_comment_id) {
              commentCopy.parent_comment_id = comment.parent_comment_id;
            }
            
            // Ensure resolved field is properly set
            if (typeof comment.resolved === 'boolean') {
              commentCopy.resolved = comment.resolved ? 1 : 0;
            } else if (comment.resolved === undefined) {
              commentCopy.resolved = 0;
            }
            
            results = [commentCopy];
          }
        }
        else if (isSelectByDocumentId && tableName === 'document_versions') {
          const documentId = bindParams[0];
          const versions = Object.values(dbStorage.documentVersions)
            .filter(v => v.documentId === documentId)
            .map(v => {
              const versionCopy = JSON.parse(JSON.stringify(v));
              
              // Ensure SQLite-specific fields are present
              if (v.metadata?.createdBy) {
                versionCopy.createdBy_id = v.metadata.createdBy.id || v.createdBy_id || 'unknown';
                versionCopy.createdBy_name = v.metadata.createdBy.name || v.createdBy_name || 'Unknown User';
                versionCopy.createdBy_role = v.metadata.createdBy.role || v.createdBy_role || 'user';
              }
              
              // Add commitMessage if available in metadata
              if (v.metadata?.commitMessage) {
                versionCopy.commitMessage = v.metadata.commitMessage;
              }
              
              // Add status from metadata if available
              if (v.metadata?.status) {
                versionCopy.status = v.metadata.status;
              } else {
                versionCopy.status = 'draft';
              }
              
              // Add createdAt from metadata if available
              if (v.metadata?.createdAt) {
                versionCopy.createdAt = v.metadata.createdAt;
              }
              
              return versionCopy;
            });
          
          results = versions;
        }
        else if (isSelectByDocumentId && tableName === 'document_comments') {
          const documentId = bindParams[0];
          const comments = Object.values(dbStorage.documentComments)
            .filter(c => c.documentId === documentId)
            .map(c => {
              const commentCopy = JSON.parse(JSON.stringify(c));
              
              // Map author fields to SQLite format
              if (c.author) {
                commentCopy.author_id = c.author.id;
                commentCopy.author_name = c.author.name;
                commentCopy.author_avatar = c.author.avatar || null;
              }
              
              // Ensure parent_comment_id is properly set
              if (c.parent_comment_id) {
                commentCopy.parent_comment_id = c.parent_comment_id;
              }
              
              // Ensure resolved field is properly set
              if (typeof c.resolved === 'boolean') {
                commentCopy.resolved = c.resolved ? 1 : 0;
              } else if (c.resolved === undefined) {
                commentCopy.resolved = 0;
              }
              
              return commentCopy;
            });
          
          results = comments;
        }
        else if (isSelectByDocumentId && tableName === 'document_edits') {
          const documentId = bindParams[0];
          const edits = Object.values(dbStorage.documentEdits)
            .filter(e => e.documentId === documentId);
          
          results = JSON.parse(JSON.stringify(edits));
        }
        else if (sql.includes('ORDER BY createdAt DESC') && tableName === 'document_versions') {
          // Handle get latest version
          const documentIdIdx = sql.indexOf('documentId = ?');
          if (documentIdIdx !== -1) {
            const documentId = bindParams[0];
            const versions = Object.values(dbStorage.documentVersions)
              .filter(v => v.documentId === documentId)
              .map(v => {
                const versionCopy = JSON.parse(JSON.stringify(v));
                
                // Ensure SQLite-specific fields are present
                if (v.metadata?.createdBy) {
                  versionCopy.createdBy_id = v.metadata.createdBy.id || v.createdBy_id || 'unknown';
                  versionCopy.createdBy_name = v.metadata.createdBy.name || v.createdBy_name || 'Unknown User';
                  versionCopy.createdBy_role = v.metadata.createdBy.role || v.createdBy_role || 'user';
                }
                
                // Add commitMessage if available in metadata
                if (v.metadata?.commitMessage) {
                  versionCopy.commitMessage = v.metadata.commitMessage;
                }
                
                // Add status from metadata if available
                if (v.metadata?.status) {
                  versionCopy.status = v.metadata.status;
                } else {
                  versionCopy.status = 'draft';
                }
                
                // Add createdAt from metadata if available
                if (v.metadata?.createdAt) {
                  versionCopy.createdAt = v.metadata.createdAt;
                }
                
                return versionCopy;
              });
            
            // Sort by createdAt in descending order
            versions.sort((a, b) => {
              const dateA = new Date(a.createdAt || a.metadata?.createdAt || 0);
              const dateB = new Date(b.createdAt || b.metadata?.createdAt || 0);
              return dateB - dateA;
            });
            
            results = versions.slice(0, 1); // Get only the first result
          }
        }
      }
      
      // If we have results, return true and increment the index
      if (currentIndex < results.length) {
        currentIndex++;
        return true;
      }
      
      return false;
    },
    
    getAsObject: function() {
      if (currentIndex > 0 && currentIndex <= results.length) {
        return results[currentIndex - 1];
      }
      return {};
    },
    
    free: function() {
      // Reset state
      bindParams = [];
      hasExecuted = false;
      results = [];
      currentIndex = 0;
    }
  };
  
  // Add jest mock functions for easier testing
  statement.bind = jest.fn(statement.bind);
  statement.step = jest.fn(statement.step);
  statement.getAsObject = jest.fn(statement.getAsObject);
  statement.free = jest.fn(statement.free);
  
  return statement;
};

// Create a mock Database implementation with state
const createMockDatabase = () => {
  return {
    exec: jest.fn().mockImplementation((sql) => {
      // Handle schema creation and other direct SQL
      if (sql.toLowerCase().includes('delete from document_edits where document')) {
        // Handle clear document edits
        const match = sql.match(/document_id\s*=\s*['"]([\w-]+)['"]/);
        if (match && match[1]) {
          const documentId = match[1];
          Object.keys(dbStorage.documentEdits).forEach(key => {
            if (dbStorage.documentEdits[key].documentId === documentId) {
              delete dbStorage.documentEdits[key];
            }
          });
        }
      }
      return [];
    }),
    
    run: jest.fn().mockImplementation((sql, params = []) => {
      // Simple implementation to handle basic run operations
      // This helps with operations like INSERT that don't need to return data
      return true;
    }),
    
    close: jest.fn(),
    
    prepare: jest.fn().mockImplementation((sql) => {
      return createMockStatement(sql);
    }),
  };
};

// Mock the sql.js module
jest.mock('sql.js', () => ({
  __esModule: true,
  default: jest.fn().mockResolvedValue({
    Database: jest.fn().mockImplementation(createMockDatabase)
  })
}));

// Helper functions for tests
global.addMockDocument = (document) => {
  dbStorage.documents[document.id] = JSON.parse(JSON.stringify(document));
};

global.addMockDocumentVersion = (version) => {
  dbStorage.documentVersions[version.id] = JSON.parse(JSON.stringify(version));
};

global.addMockDocumentComment = (comment) => {
  dbStorage.documentComments[comment.id] = JSON.parse(JSON.stringify(comment));
};

global.addMockDocumentEdit = (edit) => {
  dbStorage.documentEdits[edit.id] = JSON.parse(JSON.stringify(edit));
};

global.addMockDocumentEdits = (edits) => {
  edits.forEach(edit => {
    dbStorage.documentEdits[edit.id] = JSON.parse(JSON.stringify(edit));
  });
};

global.clearMockDocumentEditsByDocumentId = (documentId) => {
  Object.keys(dbStorage.documentEdits).forEach(key => {
    if (dbStorage.documentEdits[key].documentId === documentId) {
      delete dbStorage.documentEdits[key];
    }
  });
};

global.resetMockDatabase = () => {
  dbStorage.documents = {};
  dbStorage.documentVersions = {};
  dbStorage.documentComments = {};
  dbStorage.documentEdits = {};
};

// Mock UUID for consistent testing
global.randomUUID = () => {
  return `test-uuid-${Math.random().toString(36).substring(2, 15)}`;
};

// Mock local storage
if (!global.localStorage) {
  global.localStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
  };
}
