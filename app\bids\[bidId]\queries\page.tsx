'use client';

import { use<PERSON>arams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Loader2, MessageCircle, Clock, CheckCircle2 } from 'lucide-react';
import { useState } from 'react';
import { withErrorBoundary } from '@/components/error-boundary';

const querySchema = z.object({
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(20, 'Message must be at least 20 characters'),
  priority: z.enum(['low', 'medium', 'high']),
  category: z.enum(['technical', 'commercial', 'compliance', 'other']),
});

type QueryForm = z.infer<typeof querySchema>;

interface Query extends QueryForm {
  id: string;
  bidId: string;
  vendorId: string;
  status: 'pending' | 'answered';
  createdAt: string;
  response?: {
    message: string;
    respondedAt: string;
    respondedBy: string;
  };
}

// TODO: Replace with actual API calls when query API is implemented
// This mock data is temporary and should be replaced with real data from the API
const mockQueries: Query[] = [
  {
    id: '1',
    bidId: '123',
    vendorId: 'v1',
    subject: 'Technical Specification Clarification',
    message: 'Could you please clarify the requirements for the API integration mentioned in section 3.2?',
    priority: 'high',
    category: 'technical',
    status: 'answered',
    createdAt: '2025-03-13T14:00:00Z',
    response: {
      message: 'The API integration should follow REST principles and support OAuth 2.0 authentication...',
      respondedAt: '2025-03-13T16:00:00Z',
      respondedBy: 'John Doe',
    },
  },
  {
    id: '2',
    bidId: '123',
    vendorId: 'v1',
    subject: 'Pricing Model Question',
    message: 'Is it acceptable to propose a subscription-based pricing model instead of a perpetual license?',
    priority: 'medium',
    category: 'commercial',
    status: 'pending',
    createdAt: '2025-03-14T09:00:00Z',
  },
];

function QueriesPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [queries, setQueries] = useState<Query[]>(mockQueries);

  const form = useForm<QueryForm>({
    resolver: zodResolver(querySchema),
    defaultValues: {
      subject: '',
      message: '',
      priority: 'medium',
      category: 'technical',
    },
  });

  const handleSubmit = async (data: QueryForm) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to submit query
      const newQuery: Query = {
        ...data,
        id: Date.now().toString(),
        bidId,
        vendorId: 'v1', // TODO: Get from auth context
        status: 'pending',
        createdAt: new Date().toISOString(),
      };

      setQueries((prev) => [newQuery, ...prev]);

      toast({
        title: "Query Submitted",
        description: "Your query has been submitted successfully. You will be notified when there's a response.",
      });

      form.reset();
    } catch (error) {
      console.error('Error submitting query:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your query. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPriorityColor = (priority: QueryForm['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getCategoryColor = (category: QueryForm['category']) => {
    switch (category) {
      case 'technical':
        return 'bg-blue-500';
      case 'commercial':
        return 'bg-purple-500';
      case 'compliance':
        return 'bg-orange-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Tabs defaultValue="submit" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="submit">Submit Query</TabsTrigger>
          <TabsTrigger value="history">Query History</TabsTrigger>
        </TabsList>

        <TabsContent value="submit">
          <Card>
            <CardHeader>
              <CardTitle>Submit a Query</CardTitle>
              <CardDescription>
                Have a question about the bid? Submit your query here and we&apos;ll respond as soon as possible.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subject *</FormLabel>
                        <FormControl>
                          <Input placeholder="Brief description of your query" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Message *</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Detailed description of your query"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="priority"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Priority *</FormLabel>
                          <FormControl>
                            <select
                              className="w-full px-3 py-2 border rounded-md"
                              {...field}
                            >
                              <option value="low">Low</option>
                              <option value="medium">Medium</option>
                              <option value="high">High</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category *</FormLabel>
                          <FormControl>
                            <select
                              className="w-full px-3 py-2 border rounded-md"
                              {...field}
                            >
                              <option value="technical">Technical</option>
                              <option value="commercial">Commercial</option>
                              <option value="compliance">Compliance</option>
                              <option value="other">Other</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                    aria-label="Submit query"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      'Submit Query'
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Query History</CardTitle>
              <CardDescription>
                View and track the status of your previous queries.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-4">
                  {queries.map((query) => (
                    <Card key={query.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-semibold">{query.subject}</h4>
                            <Badge
                              variant="secondary"
                              className={getPriorityColor(query.priority)}
                            >
                              {query.priority}
                            </Badge>
                            <Badge
                              variant="secondary"
                              className={getCategoryColor(query.category)}
                            >
                              {query.category}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-500">
                            {new Date(query.createdAt).toLocaleString()}
                          </p>
                        </div>
                        <Badge
                          variant={query.status === 'answered' ? 'default' : 'secondary'}
                          className="ml-2"
                        >
                          {query.status === 'answered' ? (
                            <CheckCircle2 className="h-4 w-4 mr-1" />
                          ) : (
                            <Clock className="h-4 w-4 mr-1" />
                          )}
                          {query.status}
                        </Badge>
                      </div>
                      <div className="mt-2">
                        <p className="text-sm">{query.message}</p>
                      </div>
                      {query.response && (
                        <div className="mt-4 pl-4 border-l-2 border-gray-200">
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <MessageCircle className="h-4 w-4" />
                            <span>Response from {query.response.respondedBy}</span>
                            <span>•</span>
                            <span>{new Date(query.response.respondedAt).toLocaleString()}</span>
                          </div>
                          <p className="mt-2 text-sm">{query.response.message}</p>
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Export the component wrapped with an error boundary
export default withErrorBoundary(QueriesPage, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Queries page:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});
